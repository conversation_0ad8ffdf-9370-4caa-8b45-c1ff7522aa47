# encoding: utf-8
import pandas as pd
import os
import sys

# 添加路径
new_path = ['D:\\0.维护代码\\1.数据库更新维护', ]
for path in new_path:
    if path not in sys.path:
        sys.path.append(path)

from wind_database_maintain import wind_remote_fun, db_local_fun

def delete_tables(connection, table_names):
    """删除指定的表"""
    cursor = connection.cursor()
    deleted_tables = []
    error_tables = []
    
    for table_name in table_names:
        try:
            cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")
            deleted_tables.append(table_name)
            print(f"成功删除表: {table_name}")
        except Exception as e:
            error_tables.append({"表名": table_name, "错误": str(e)})
            print(f"删除表 {table_name} 时出错: {e}")
    
    connection.commit()
    return deleted_tables, error_tables

def main():
    # 连接本地数据库
    db_local = db_local_fun()
    
    try:
        windhash_tables = []
        discarded_tables = []
        
        # 读取windhash表列表（如果文件存在）
        if os.path.exists("windhash_tables.txt"):
            with open("windhash_tables.txt", "r", encoding="utf-8") as f:
                windhash_tables = [line.strip() for line in f if line.strip()]
            print(f"从windhash_tables.txt读取了 {len(windhash_tables)} 个表")
        
        # 读取废弃表列表（如果文件存在）
        if os.path.exists("discarded_tables.txt"):
            discarded_df = pd.read_csv("discarded_tables.txt", sep="\t", encoding="utf-8")
            discarded_tables = discarded_df["表名"].tolist()
            print(f"从discarded_tables.txt读取了 {len(discarded_tables)} 个表")
        
        # 确认是否删除
        if not windhash_tables and not discarded_tables:
            print("没有找到需要删除的表，请先运行get_large_tables.py获取表信息")
            return
        
        print(f"即将删除 {len(windhash_tables)} 个包含'windhash'的表和 {len(discarded_tables)} 个包含'废弃'描述的表")
        confirm = input("确定要删除这些表吗？(y/n): ")
        
        if confirm.lower() != 'y':
            print("操作已取消")
            return
        
        # 删除windhash表
        if windhash_tables:
            print("开始删除包含'windhash'的表...")
            deleted, errors = delete_tables(db_local, windhash_tables)
            print(f"成功删除 {len(deleted)} 个包含'windhash'的表")
            if errors:
                error_df = pd.DataFrame(errors)
                error_df.to_csv("windhash_delete_errors.txt", sep="\t", index=False, encoding="utf-8")
                print(f"删除 {len(errors)} 个表时出错，详细信息已保存到 windhash_delete_errors.txt")
        
        # 删除废弃表
        if discarded_tables:
            print("开始删除包含'废弃'描述的表...")
            deleted, errors = delete_tables(db_local, discarded_tables)
            print(f"成功删除 {len(deleted)} 个包含'废弃'描述的表")
            if errors:
                error_df = pd.DataFrame(errors)
                error_df.to_csv("discarded_delete_errors.txt", sep="\t", index=False, encoding="utf-8")
                print(f"删除 {len(errors)} 个表时出错，详细信息已保存到 discarded_delete_errors.txt")
        
        print("任务完成!")
        
    finally:
        db_local.close()

if __name__ == "__main__":
    main() 