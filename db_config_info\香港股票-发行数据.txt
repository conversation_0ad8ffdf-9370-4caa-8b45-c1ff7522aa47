{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "香港股票发行数据[HKShareIssuance]", "ID": "HKShareIssuance", "HasAuth": true, "sortOrder": 0, "IsContract": true, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false, "IsRoot": false}, {"DataType": 1, "Text": "香港股票中介机构[HKShareAgency]", "ID": "HKShareAgency", "HasAuth": true, "sortOrder": 1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#C5C5C5", "AuthType": 0, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}], "PageInfo": null}, "success": true}