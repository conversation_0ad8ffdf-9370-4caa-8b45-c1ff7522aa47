{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "中国A股并购事件[MergerEvent]", "ID": "MergerEvent", "HasAuth": true, "sortOrder": -5, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国A股并购事件参与方[MergerParticipant]", "ID": "MergerParticipant", "HasAuth": true, "sortOrder": -4, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国A股并购事件中介机构[MergerAgency]", "ID": "MergerAgency", "HasAuth": true, "sortOrder": -3, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国A股关联事件[RelatedEvent]", "ID": "RelatedEvent", "HasAuth": true, "sortOrder": -2, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国A股前瞻性情报[MergerIntelligence]", "ID": "MergerIntelligence", "HasAuth": true, "sortOrder": -1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国A股盈利承诺明细表[CommitProfit]", "ID": "CommitProfit", "HasAuth": true, "sortOrder": 0, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国A股盈利承诺汇总表[CommitProfitSummary]", "ID": "CommitProfitSummary", "HasAuth": true, "sortOrder": 1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}], "PageInfo": null}, "success": true}