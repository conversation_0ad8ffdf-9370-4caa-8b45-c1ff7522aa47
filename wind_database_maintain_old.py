# encoding: utf-8
import time
import re
import pandas as pd
import pymysql
import os

wind_remote = pymysql.connect(
    host='*************',
    port=3306,
    user='inforesdep01',
    password='tfyfInfo@1602',
    db='wind',
    charset='utf8'
)

db_local = pymysql.connect(
    host='localhost',
    port=3306,
    user='root',
    password='root',
    db='wind',
    charset='utf8'
)


def convert_to_mysql_type(pymysql_type):
    map_dict = {
        pymysql.constants.FIELD_TYPE.VARCHAR: 'VARCHAR',
        pymysql.constants.FIELD_TYPE.CHAR: 'CHAR',
        pymysql.constants.FIELD_TYPE.TINY: 'TINYINT',
        pymysql.constants.FIELD_TYPE.LONG: 'INT',
        pymysql.constants.FIELD_TYPE.FLOAT: 'FLOAT',
        pymysql.constants.FIELD_TYPE.DOUBLE: 'DOUBLE',
        pymysql.constants.FIELD_TYPE.DECIMAL: 'DECIMAL',
        pymysql.constants.FIELD_TYPE.NEWDECIMAL: 'DECIMAL',
        pymysql.constants.FIELD_TYPE.LONGLONG: 'BIGINT',
        pymysql.constants.FIELD_TYPE.DATE: 'DATE',
        pymysql.constants.FIELD_TYPE.TIME: 'TIME',
        pymysql.constants.FIELD_TYPE.DATETIME: 'DATETIME',
        pymysql.constants.FIELD_TYPE.TIMESTAMP: 'TIMESTAMP',
        pymysql.constants.FIELD_TYPE.LONG_BLOB: 'LONGBLOB',
        pymysql.constants.FIELD_TYPE.MEDIUM_BLOB: 'MEDIUMBLOB',
        pymysql.constants.FIELD_TYPE.BLOB: 'LONGTEXT',
        pymysql.constants.FIELD_TYPE.TINY_BLOB: 'TINYBLOB',
        pymysql.constants.FIELD_TYPE.STRING: 'CHAR',
        pymysql.constants.FIELD_TYPE.JSON: 'JSON'
    }
    return map_dict.get(pymysql_type, 'VARCHAR')


def get_all_table_names(connector):
    cur = connector.cursor()
    cur.execute("SHOW TABLES")
    results = cur.fetchall()
    tables = [result[0] for result in results]
    return tables


def creat_new_table_frame(table_name):
    cursor_remote = wind_remote.cursor()
    cursor_local = db_local.cursor()
    cursor_remote.execute(f"SELECT * FROM {table_name} LIMIT 0")
    desc = cursor_remote.description
    sql_create_table = ""
    for column in desc:
        field_name = column[0]
        field_type = column[1]
        internal_size = column[3]
        scale = column[5]
        null_ok = column[6]
        type = convert_to_mysql_type(field_type)
        if type in ["VARCHAR"]:
            type = type + f"({internal_size})"
        if type in ["DECIMAL"]:
            type = type + f"({internal_size},{scale})"
        if null_ok == False:
            type += " PRIMARY KEY"
        sql_create_table += f"`{field_name}`" + " " + type + ", "

    sql_create_table = sql_create_table[:-2]
    sql_create_table = f"CREATE TABLE {table_name} ({sql_create_table})"
    # 创建新表
    cursor_local.execute(sql_create_table)
    db_local.commit()


def contains_substring(A, B):
    return any(substr in A for substr in B)


def write_new_table_all_data(table_name):
    cursor_remote = wind_remote.cursor()
    cursor_local = db_local.cursor()
    sql_fetch = f"SELECT * FROM {table_name}"
    cursor_remote.execute(sql_fetch)
    data = cursor_remote.fetchall()
    desc = cursor_remote.description

    columns = [column[0] for column in desc]
    column_sql = f'({",".join(columns)})'
    column_types = [convert_to_mysql_type(column[1]) for column in desc]
    value_all = ''
    for row in data:
        new_row = []
        for i in range(len(row)):
            if row[i] == None:
                new_row.append("NULL")
            elif columns[i] == 'OBJECT_ID':
                new_row.append(f'"{row[i]}"')
            elif column_types[i] in ['DECIMAL', 'INT', 'TINYINT', 'INT', 'FLOAT', 'DOUBLE', 'BIGINT', ]:
                new_row.append(str(row[i]))
            elif contains_substring(column_types[i], ['CHAR', 'LONGTEXT']):
                if '"' in row[i]:
                    if "'" in row[i]:
                        new_row.append(f"''{row[i]}''")
                    else:
                        new_row.append(f"'{row[i]}'")
                else:
                    new_row.append(f'"{row[i]}"')
            else:
                new_row.append(f'"{row[i]}"')
        value = f"({','.join(new_row)})"
        value_all += value + ","
    value_all = value_all[:-1]

    sql_insert = f"INSERT INTO {table_name} {column_sql} VALUES {value_all}"
    cursor_local.execute(sql_insert)
    db_local.commit()


def write_new_table_separately(table_name):
    cursor_remote = wind_remote.cursor()
    cursor_local = db_local.cursor()
    total_rows = get_table_count(wind_remote, table_name)
    batch_size = 1000
    desc = None
    columns = None
    column_sql = None
    column_types = None
    # 分批次获取数据并写入
    for offset in range(0, total_rows, batch_size):
        sql_fetch = f"SELECT * FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
        cursor_remote.execute(sql_fetch)
        data = cursor_remote.fetchall()

        if desc is None:
            desc = cursor_remote.description
            columns = [column[0] for column in desc]
            column_sql = f'({",".join(columns)})'
            column_types = [convert_to_mysql_type(column[1]) for column in desc]

        value_all = ''
        for row in data:
            new_row = []
            for i in range(len(row)):
                if row[i] == None:
                    new_row.append("NULL")
                elif columns[i] == 'OBJECT_ID':
                    new_row.append(f'"{row[i]}"')
                elif column_types[i] in ['DECIMAL', 'INT', 'TINYINT', 'INT', 'FLOAT', 'DOUBLE', 'BIGINT']:
                    new_row.append(str(row[i]))
                elif contains_substring(column_types[i], ['CHAR', 'LONGTEXT']):
                    if '"' in row[i]:
                        if "'" in row[i]:
                            new_row.append(f"''{row[i]}''")
                        else:
                            new_row.append(f"'{row[i]}'")
                    else:
                        new_row.append(f'"{row[i]}"')
                else:
                    new_row.append(f'"{row[i]}"')
            value = f"({','.join(new_row)})"
            value_all += value + ","
        value_all = value_all[:-1]

        sql_insert = f"INSERT INTO {table_name} {column_sql} VALUES {value_all}"
        cursor_local.execute(sql_insert)
        print(f'已经写入比例：{(offset + batch_size) / total_rows}')
        db_local.commit()


def update_table_separately(table_name):
    """
    分批更新数据,
    :param table_name:
    :return:
    """
    cursor_remote = wind_remote.cursor()
    cursor_local = db_local.cursor()
    exist_rows = get_table_count(db_local, table_name)
    total_rows = get_table_count(wind_remote, table_name)
    batch_size = 1000
    desc = None
    columns = None
    column_sql = None
    column_types = None
    # 分批次获取数据并写入
    for offset in range(exist_rows, total_rows, batch_size):
        sql_fetch = f"SELECT * FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
        cursor_remote.execute(sql_fetch)
        data = cursor_remote.fetchall()

        if desc is None:
            desc = cursor_remote.description
            columns = [column[0] for column in desc]
            column_sql = f'({",".join(columns)})'
            column_types = [convert_to_mysql_type(column[1]) for column in desc]

        value_all = ''
        for row in data:
            new_row = []
            for i in range(len(row)):
                if row[i] == None:
                    new_row.append("NULL")
                elif columns[i] == 'OBJECT_ID':
                    new_row.append(f'"{row[i]}"')
                elif column_types[i] in ['DECIMAL', 'INT', 'TINYINT', 'INT', 'FLOAT', 'DOUBLE', 'BIGINT']:
                    new_row.append(str(row[i]))
                elif contains_substring(column_types[i], ['CHAR', 'LONGTEXT']):
                    if '"' in row[i]:
                        if "'" in row[i]:
                            new_row.append(f"''{row[i]}''")
                        else:
                            new_row.append(f"'{row[i]}'")
                    else:
                        new_row.append(f'"{row[i]}"')
                else:
                    new_row.append(f'"{row[i]}"')
            value = f"({','.join(new_row)})"
            value_all += value + ","
        value_all = value_all[:-1]

        sql_insert = f"INSERT INTO {table_name} {column_sql} VALUES {value_all}"

        def _insert_handle_dup(sql_insert, cursor_local, db_local, table_name):
            try:
                cursor_local.execute(sql_insert)
                db_local.commit()
            except pymysql.err.IntegrityError as e:
                if e.args[0] == 1062:  # Duplicate entry
                    duplicate_key = re.search(r"'(.*?)'", e.args[1]).group(1)  # 提取重复的键
                    print(f'Duplicate key detected: {duplicate_key}, attempting to delete...')
                    sql = f"SHOW KEYS FROM {table_name} WHERE Key_name = 'PRIMARY'"
                    cursor_local.execute(sql)
                    primary_key = cursor_local.fetchone()[4]
                    sql_delete = f"DELETE FROM {table_name} WHERE {primary_key} = '{duplicate_key}'"
                    cursor_local.execute(sql_delete)
                    _insert_handle_dup(sql_insert, cursor_local, db_local, table_name)
                else:
                    raise e

        _insert_handle_dup(sql_insert, cursor_local, db_local, table_name)
        print(f'已经写入比例：{(offset + batch_size) / total_rows}')
        db_local.commit()


def get_table_size(table_name):
    cursor_remote = wind_remote.cursor()
    sql = f"SELECT round(((data_length + index_length) / 1024 / 1024), 2) `Size in MB` FROM information_schema.TABLES WHERE table_schema = 'wind' AND table_name = '{table_name}'"
    cursor_remote.execute(sql)
    result = cursor_remote.fetchone()
    return result[0]


def get_table_count(connect, table_name):
    cursor = connect.cursor()
    sql = f"SELECT COUNT(*) FROM {table_name}"
    cursor.execute(sql)
    result = cursor.fetchone()
    return result[0]


def get_local_table_update_time(table_name):
    cur = db_local.cursor()
    sql = f"SELECT MAX(OPDATE) FROM {table_name}"
    cur.execute(sql)
    result = cur.fetchone()
    return result[0]


def get_added_data(table_name, opdate_max):
    cur = wind_remote.cursor()
    cur.execute(f"SELECT * FROM {table_name} WHERE OPDATE > %s", (opdate_max,))
    results = cur.fetchall()
    return results, cur.description


def update_table(table_name):

    def _insert_handle_dup(sql_insert, cursor_local, db_local, table_name):
        try:
            cursor_local.execute(sql_insert)
            db_local.commit()
        except pymysql.err.IntegrityError as e:
            if e.args[0] == 1062:  # Duplicate entry
                duplicate_key = re.search(r"'(.*?)'", e.args[1]).group(1)  # 提取重复的键
                print(f'Duplicate key detected: {duplicate_key}, attempting to delete...')
                sql = f"SHOW KEYS FROM {table_name} WHERE Key_name = 'PRIMARY'"
                cursor_local.execute(sql)
                primary_key = cursor_local.fetchone()[4]
                sql_delete = f"DELETE FROM {table_name} WHERE {primary_key} = '{duplicate_key}'"
                cursor_local.execute(sql_delete)
                _insert_handle_dup(sql_insert, cursor_local, db_local, table_name)

    cursor_local = db_local.cursor()
    opdate_max = get_local_table_update_time(table_name)
    data, desc = get_added_data(table_name, opdate_max)
    if len(data) == 0:
        return
    columns = [column[0] for column in desc]
    column_sql = f'({",".join(columns)})'
    column_types = [convert_to_mysql_type(column[1]) for column in desc]

    batch_size = 100
    for start_idx in range(0, len(data), batch_size):
        value_all = ''
        for row in data[start_idx:start_idx+batch_size]:
            new_row = []
            for i in range(len(row)):
                if row[i] == None:
                    new_row.append("NULL")
                elif columns[i] == 'OBJECT_ID':
                    new_row.append(f'"{row[i]}"')
                elif column_types[i] in ['DECIMAL', 'INT', 'TINYINT', 'INT', 'FLOAT', 'DOUBLE', 'BIGINT', ]:
                    new_row.append(str(row[i]))
                elif contains_substring(column_types[i], ['CHAR', 'LONGTEXT']):
                    if '"' in row[i]:
                        if "'" in row[i]:
                            new_row.append(f"''{row[i]}''")
                        else:
                            new_row.append(f"'{row[i]}'")
                    else:
                        new_row.append(f'"{row[i]}"')
                else:
                    new_row.append(f'"{row[i]}"')

            value = f"({','.join(new_row)})"
            value_all += value + ","
        value_all = value_all[:-1]

        sql_insert = f"INSERT INTO {table_name} {column_sql} VALUES {value_all}"
        _insert_handle_dup(sql_insert, cursor_local, db_local, table_name)



def update_by_object_id(table_name):
    cursor_remote = wind_remote.cursor()
    cursor_local = db_local.cursor()

    # 获取远程和本地的所有OBJECT_ID
    cursor_remote.execute(f"SELECT OBJECT_ID FROM {table_name}")
    object_ids_remote = set([row[0] for row in cursor_remote.fetchall()])

    cursor_local.execute(f"SELECT OBJECT_ID FROM {table_name}")
    object_ids_local = set([row[0] for row in cursor_local.fetchall()])

    # 获取缺失的OBJECT_ID
    missing_ids = object_ids_remote - object_ids_local

    # 分批获取缺失的数据并写入
    for object_id in missing_ids:
        cursor_remote.execute(f"SELECT * FROM {table_name} WHERE OBJECT_ID = %s", (object_id,))
        row = cursor_remote.fetchone()
        columns = [column[0] for column in cursor_remote.description]
        column_sql = ', '.join(columns)
        values = ', '.join(['%s'] * len(row))
        sql_insert = f"INSERT INTO {table_name} ({column_sql}) VALUES ({values})"
        cursor_local.execute(sql_insert, row)
        db_local.commit()


def update_with_opmode_1(table_name):
    cursor_remote = wind_remote.cursor()
    cursor_local = db_local.cursor()

    cursor_remote.execute(f"SELECT * FROM {table_name} WHERE OPMODE = 1")
    rows = cursor_remote.fetchall()
    desc = cursor_remote.description
    columns = [column[0] for column in desc]
    column_sql = ', '.join(columns)
    for row in rows:
        object_id = row[columns.index("OBJECT_ID")]
        cursor_local.execute(f"DELETE FROM {table_name} WHERE OBJECT_ID = %s", (object_id,))
        values = ', '.join(['%s'] * len(row))
        sql_insert = f"INSERT INTO {table_name} ({column_sql}) VALUES ({values})"
        cursor_local.execute(sql_insert, row)
        db_local.commit()





remote_table_list = get_all_table_names(wind_remote)
local_table_list = get_all_table_names(db_local)

remote_table_list = ['aindexeodprices', 'ashareeodprices', 'ashareyield', 'ashareanntext', 'ashareearningest',
                     'asharel2indicators', 'asharemoneyflow', 'ashareeodderivativeindicator']
remote_table_list = ['aindexvaluation','aindexwindindustrieseod','ashareaftereodpindicators','ashareincome','ashareinsideholder']
remote_table_list = ['ashareaftereodpindicators','ashareincome','ashareinsideholder']

for table_name in remote_table_list:
    size = get_table_size(table_name)
    # if size > 1024:
    #     continue
    print(f"开始更新{table_name}...")
    try:
        if table_name not in local_table_list:
            print(f"本地没有{table_name}，开始创建...")
            creat_new_table_frame(table_name)
            print(f"创建{table_name}成功！")
            print(f"开始写入{table_name}...")
            num_remote = get_table_count(wind_remote, table_name)
            if num_remote < 10000:
                write_new_table_all_data(table_name)
            else:
                print(f"分批写入{table_name}")
                write_new_table_separately(table_name)
            print(f"写入{table_name}成功！")
            print(f"开始检查{table_name}...")
            num_local = get_table_count(db_local, table_name)
            print(f"远程数量：{num_remote}")
            print(f"本地数量：{num_local}")
            if num_remote != num_local:
                print(f"{table_name}数量不相等！")
                # ---------------------------------------------------------------------------------------------------
                #     增加一个函数，通过比对id，来新写入数据

            else:
                print(f"写入{table_name}成功！")
        else:
            print(f"本地已有{table_name}，开始更新...")
            num_remote = get_table_count(wind_remote, table_name)
            num_local = get_table_count(db_local, table_name)
            if num_local == 0:
                if num_remote < 10000:
                    write_new_table_all_data(table_name)
                else:
                    print(f"分批写入{table_name}")
                    write_new_table_separately(table_name)
            else:
                update_table(table_name)
                # if num_remote - num_local < 10000:
                #     update_table(table_name)
                # else:
                #     update_table_separately(table_name)
            print(f"更新{table_name}成功！增加{num_remote - num_local}条数据！")
            print(f"开始检查{table_name}...")
            num_remote = get_table_count(wind_remote, table_name)
            num_local = get_table_count(db_local, table_name)
            print(f"远程数量：{num_remote}")
            print(f"本地数量：{num_local}")
            if num_remote != num_local:
                print(f"{table_name}数量不相等！")
                # ---------------------------------------------------------------------------------------------------
            #     增加一个函数，通过比对id，来新写入数据
            else:
                print(f"更新{table_name}成功！")
    except Exception as e:
        print(f"{table_name}更新失败！")
        print(e)

    print(f"{table_name}更新完毕！")
    print("---------------------------------------------------")


# 1. 创建新表时需要指定id
# 2. 写入要保证数据格式一致
# 3. 要先从本地获取最新的更新日期，远程获取大于这个日期的数据，再写入本地
# 4. 写入本地时要注意id是否是重复的，如果重复，就不写入（或者删除旧的，写入新的）
# 5. 更新完后，要获取总体的数据个数据，如果远程和本地不一致，就是有问题的，要报错
# 6. 定时更新运行，每天晚上更新一次

# 更新的思路有两种，第一种是交易类数据，按照日期更新
# 第二种是其他数据，按照id更新，本地保存一个的id，或者更新时先获取本地的id和远程的id，然后比较，获取远程新的
# 远程中OPMODE为1的数据，然后写入本地，删除本地旧的数据，这样可以保证数据的一致性

# 6.数据表入库后，其中OPMODE，OPDATE,MOPDATE表示什么意思
# ——这三个字段非业务属性，为记录系统信息，具体
#         OPMODE: 表示数据更新动作，0 - 新增，1 - 修改；
#         OPDATE: 表示数据(包)在服务端的生成时间；
#         MOPDATE:表示数据记录在底表的操作时间。


