{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "香港股票行情日收益率[HKShareYield]", "ID": "HKShareYield", "HasAuth": true, "sortOrder": 0, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "香港股票日行情估值指标[HKShareEODDerivativeIndex]", "ID": "HKShareEODDerivativeIndex", "HasAuth": true, "sortOrder": 1, "IsContract": true, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}], "PageInfo": null}, "success": true}