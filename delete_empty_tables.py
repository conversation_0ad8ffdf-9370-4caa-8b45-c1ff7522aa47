#!/usr/bin/env python
# encoding: utf-8
import pymysql
import sys
import os
import argparse

# 添加项目路径
current_path = os.path.dirname(os.path.abspath(__file__))
if current_path not in sys.path:
    sys.path.append(current_path)

def db_connect(db_name="wind"):
    """获取指定数据库连接"""
    db_info = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'db': db_name,
        'charset': 'utf8'
    }
    return pymysql.connect(**db_info)

def get_all_databases():
    """获取所有数据库名称"""
    conn = pymysql.connect(
        host='localhost',
        port=3306,
        user='root',
        password='root',
        charset='utf8'
    )
    cursor = conn.cursor()
    try:
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall() if db[0] not in ['information_schema', 'mysql', 'performance_schema', 'sys']]
        return databases
    except Exception as e:
        print(f"获取数据库列表失败: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def get_all_tables(db_name):
    """获取指定数据库中所有表名"""
    conn = db_connect(db_name)
    cursor = conn.cursor()
    try:
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        return tables
    except Exception as e:
        print(f"获取表名失败: {e}")
        return []
    finally:
        cursor.close()
        conn.close()

def get_table_count(db_name, table_name):
    """获取表中数据行数"""
    conn = db_connect(db_name)
    cursor = conn.cursor()
    try:
        cursor.execute(f"SELECT 1 FROM `{table_name}` LIMIT 1")
        result = cursor.fetchone()
        return 0 if result is None else 1
    except Exception as e:
        print(f"检查表 {table_name} 是否为空失败: {e}")
        return -1
    finally:
        cursor.close()
        conn.close()

def delete_empty_table(db_name, table_name):
    """删除空表"""
    conn = db_connect(db_name)
    cursor = conn.cursor()
    try:
        cursor.execute(f"DROP TABLE `{table_name}`")
        conn.commit()
        return True
    except Exception as e:
        print(f"删除表 {table_name} 失败: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def main():
    """主函数：查找并删除空表"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='删除MySQL数据库中的空表')
    parser.add_argument('-d', '--database', help='指定要操作的数据库名称')
    parser.add_argument('-a', '--all', action='store_true', help='显示所有表是否为空')
    parser.add_argument('-y', '--yes', action='store_true', help='自动确认删除')
    args = parser.parse_args()

    # 获取数据库列表
    if not args.database:
        databases = get_all_databases()
        print(f"发现以下数据库:")
        for i, db in enumerate(databases, 1):
            print(f"{i}. {db}")
        
        db_choice = input("请选择要操作的数据库序号(输入q退出): ")
        if db_choice.lower() == 'q':
            print("操作已取消")
            return
        
        try:
            db_index = int(db_choice) - 1
            if 0 <= db_index < len(databases):
                db_name = databases[db_index]
            else:
                print("无效的选择，操作已取消")
                return
        except ValueError:
            print("无效的输入，操作已取消")
            return
    else:
        db_name = args.database
    
    print(f"已选择数据库: {db_name}")
    
    # 获取所有表
    tables = get_all_tables(db_name)
    print(f"数据库 '{db_name}' 中共有 {len(tables)} 个表")
    
    if args.all:
        print("所有表的状态:")
        for table in tables:
            count = get_table_count(db_name, table)
            status = "空表" if count == 0 else "非空表"
            print(f"表 '{table}' 状态: {status}")
    
    empty_tables = []
    # 获取空表
    for table in tables:
        count = get_table_count(db_name, table)
        if count == 0:
            empty_tables.append(table)
    
    print(f"发现 {len(empty_tables)} 个空表")
    
    # 询问是否删除
    if empty_tables:
        print("以下是空表列表:")
        for i, table in enumerate(empty_tables, 1):
            print(f"{i}. {table}")
        
        if not args.yes:
            confirm = input("是否删除这些空表？(y/n): ")
        else:
            confirm = 'y'
            
        if confirm.lower() == 'y':
            success_count = 0
            for table in empty_tables:
                if delete_empty_table(db_name, table):
                    print(f"成功删除空表: {table}")
                    success_count += 1
                else:
                    print(f"删除空表 {table} 失败")
            
            print(f"共删除 {success_count} 个空表")
        else:
            print("操作已取消")
    else:
        print("数据库中没有空表")

if __name__ == "__main__":
    main() 