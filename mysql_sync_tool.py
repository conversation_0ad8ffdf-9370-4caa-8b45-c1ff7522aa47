#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MySQL数据同步工具
功能：将源数据库的数据同步到目标数据库
用法：
  1. 直接运行 python mysql_sync_tool.py 使用代码中配置的数据库信息
  2. 或者使用参数指定: python mysql_sync_tool.py -s [源数据库配置] -d [目标数据库配置]
"""

import sys
import os
import time
import logging
import pymysql
import argparse
import concurrent.futures
from typing import Dict, List, Tuple, Optional, Any, Union
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime

# 源数据库配置
SOURCE_DB_CONFIG = {
        'host': '**************',
        'port': 3306,
        'user': 'cusreader',
        'password': 'cus_reader@tf1603',
        'database': 'cus_fund_db2',
        'charset': 'utf8'
    }

# 目标数据库配置
TARGET_DB_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'database': 'cus_fund_db2',
        'charset': 'utf8'
    }


# 并发线程数
MAX_WORKERS = 1

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"mysql_sync_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("MySQL同步工具")

class DatabaseConfig:
    """数据库配置类"""
    def __init__(self, host: str, port: int, user: str, password: str, database: str):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'DatabaseConfig':
        """从字典创建数据库配置"""
        return cls(
            host=config_dict["host"],
            port=config_dict["port"],
            user=config_dict["user"],
            password=config_dict["password"],
            database=config_dict["database"]
        )
    
    @classmethod
    def from_string(cls, config_str: str) -> 'DatabaseConfig':
        """从字符串解析数据库配置
        格式: user:password@host:port/database
        """
        try:
            auth, location = config_str.split('@')
            user, password = auth.split(':')
            host_port, database = location.split('/')
            if ':' in host_port:
                host, port = host_port.split(':')
                port = int(port)
            else:
                host = host_port
                port = 3306
            
            return cls(host, port, user, password, database)
        except Exception as e:
            logger.error(f"解析数据库配置字符串失败: {str(e)}")
            raise ValueError(f"无效的数据库配置格式: {config_str}. 正确格式: user:password@host:port/database")

class MySQLSyncTool:
    """MySQL数据同步工具类"""
    def __init__(self, source_config: DatabaseConfig, target_config: DatabaseConfig):
        self.source_config = source_config
        self.target_config = target_config
        self.source_conn = None
        self.target_conn = None
        self.max_workers = MAX_WORKERS  # 并发线程数
        self.incremental_mode = True   # 增量同步模式
    
    def connect_database(self, config: DatabaseConfig) -> pymysql.connections.Connection:
        """连接到数据库"""
        try:
            conn = pymysql.connect(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            return conn
        except Exception as e:
            logger.error(f"连接到数据库 {config.host}:{config.port}/{config.database} 失败: {str(e)}")
            raise
    
    def setup_connections(self):
        """设置数据库连接"""
        logger.info("正在连接源数据库...")
        self.source_conn = self.connect_database(self.source_config)
        logger.info(f"成功连接到源数据库: {self.source_config.host}:{self.source_config.port}/{self.source_config.database}")
        
        logger.info("正在连接目标数据库...")
        self.target_conn = self.connect_database(self.target_config)
        logger.info(f"成功连接到目标数据库: {self.target_config.host}:{self.target_config.port}/{self.target_config.database}")
    
    def close_connections(self):
        """关闭数据库连接"""
        if self.source_conn:
            self.source_conn.close()
            logger.info("已关闭源数据库连接")
        
        if self.target_conn:
            self.target_conn.close()
            logger.info("已关闭目标数据库连接")
    
    def get_source_tables(self) -> List[str]:
        """获取源数据库中的所有表名"""
        with self.source_conn.cursor() as cursor:
            cursor.execute("SHOW TABLES")
            tables = [list(row.values())[0] for row in cursor.fetchall()]
        logger.info(f"源数据库中发现 {len(tables)} 个表")
        return tables
    
    def get_table_structure(self, table_name: str) -> str:
        """获取表结构创建语句"""
        with self.source_conn.cursor() as cursor:
            cursor.execute(f"SHOW CREATE TABLE `{table_name}`")
            result = cursor.fetchone()
            create_stmt = result.get('Create Table') or result.get(f'Create Table {table_name}')
            if not create_stmt:
                # 获取第二个值
                create_stmt = list(result.values())[1]
        return create_stmt
    
    def get_table_primary_key(self, table_name: str, connection) -> Union[str, List[str], None]:
        """获取表的主键字段，如果有多个主键字段则返回列表"""
        try:
            with connection.cursor() as cursor:
                # 先获取当前连接的数据库名称
                cursor.execute("SELECT DATABASE()")
                db_name = cursor.fetchone()['DATABASE()']
                logger.info(f"获取表 {table_name} 主键, 数据库名称: {db_name}")
                
                # 查询主键信息
                cursor.execute(f"""
                    SELECT COLUMN_NAME
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                    WHERE TABLE_SCHEMA = %s
                    AND TABLE_NAME = %s
                    AND CONSTRAINT_NAME = 'PRIMARY'
                    ORDER BY ORDINAL_POSITION
                """, (db_name, table_name))
                primary_keys = [row['COLUMN_NAME'] for row in cursor.fetchall()]
                
                if not primary_keys:
                    logger.warning(f"表 {table_name} 没有定义主键")
                    return None
                
                logger.info(f"表 {table_name} 的主键字段: {primary_keys}")
                return primary_keys[0] if len(primary_keys) == 1 else primary_keys
        except Exception as e:
            logger.error(f"获取表 {table_name} 主键失败: {str(e)}")
            return None
    
    def create_table_in_target(self, table_name: str, create_stmt: str) -> bool:
        """在目标数据库中创建表"""
        try:
            with self.target_conn.cursor() as cursor:
                # 检查表是否存在
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                table_exists = cursor.fetchone() is not None
                
                if table_exists:
                    # 如果表已存在，不删除表，而是保留数据
                    logger.info(f"目标数据库中表 {table_name} 已存在，保留现有数据")
                    return True
                else:
                    # 创建表
                    cursor.execute(create_stmt)
                    self.target_conn.commit()
                    logger.info(f"成功在目标数据库中创建表: {table_name}")
                    return True
        except Exception as e:
            logger.error(f"在目标数据库中创建或检查表 {table_name} 失败: {str(e)}")
            return False
    
    def count_table_rows(self, table_name: str) -> int:
        """计算表中行数"""
        with self.source_conn.cursor() as cursor:
            cursor.execute(f"SELECT COUNT(*) as count FROM `{table_name}`")
            result = cursor.fetchone()
            return result['count']
    
    def sync_table_data(self, table_name: str, batch_size: int = 20000) -> bool:
        """同步表数据（根据模式选择增量同步或全量同步）"""
        # 如果不是增量模式，则使用全量同步
        logger.info(f"表 {table_name} 同步模式: {'增量同步' if self.incremental_mode else '全量同步'}")
        if not self.incremental_mode:
            return self.sync_table_data_full(table_name, batch_size)
        
        try:
            # 获取表的主键
            primary_key = self.get_table_primary_key(table_name, self.source_conn)
            if not primary_key:
                logger.warning(f"表 {table_name} 没有主键，无法进行增量同步，将进行全量同步")
                return self.sync_table_data_full(table_name, batch_size)
            
            logger.info(f"表 {table_name} 使用主键 {primary_key} 进行增量同步")
            
            # 获取表的列
            with self.source_conn.cursor() as cursor:
                cursor.execute(f"SHOW COLUMNS FROM `{table_name}`")
                columns = [row['Field'] for row in cursor.fetchall()]
            
            columns_str = ", ".join([f"`{col}`" for col in columns])
            placeholders = ", ".join(["%s" for _ in columns])
            
            # 获取源表和目标表的主键最大值
            source_max_id = self.get_max_id(table_name, primary_key, self.source_conn)
            target_max_id = self.get_max_id(table_name, primary_key, self.target_conn)
            
            if target_max_id is None:
                # 目标表为空，进行全量同步
                logger.info(f"目标表 {table_name} 为空，进行全量同步")
                return self.sync_table_data_full(table_name, batch_size)
            
            if source_max_id <= target_max_id:
                logger.info(f"表 {table_name} 源数据库最大ID({source_max_id})小于等于目标数据库最大ID({target_max_id})，无需同步")
                return True
            
            # 计算需要同步的记录数
            with self.source_conn.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) as count FROM `{table_name}` WHERE `{primary_key}` > %s", (target_max_id,))
                total_rows = cursor.fetchone()['count']
            
            logger.info(f"表 {table_name} 有 {total_rows} 条新增记录需要同步")
            
            if total_rows == 0:
                logger.info(f"表 {table_name} 没有新数据需要同步")
                return True
            
            # 准备插入/更新语句
            insert_sql = f"INSERT INTO `{table_name}` ({columns_str}) VALUES ({placeholders}) ON DUPLICATE KEY UPDATE "
            update_parts = [f"`{col}` = VALUES(`{col}`)" for col in columns if col != primary_key]
            insert_sql += ", ".join(update_parts)
            
            # 分批同步数据
            offset = 0
            processed_rows = 0
            while processed_rows < total_rows:
                with self.source_conn.cursor() as source_cursor:
                    source_cursor.execute(
                        f"SELECT * FROM `{table_name}` WHERE `{primary_key}` > %s ORDER BY `{primary_key}` LIMIT %s",
                        (target_max_id, batch_size)
                    )
                    batch_data = source_cursor.fetchall()
                
                if not batch_data:
                    break
                
                # 准备插入数据
                values = []
                for row in batch_data:
                    row_values = [row[col] for col in columns]
                    values.append(row_values)
                    target_max_id = max(target_max_id, row[primary_key])
                
                # 执行批量插入或更新
                with self.target_conn.cursor() as target_cursor:
                    target_cursor.executemany(insert_sql, values)
                self.target_conn.commit()
                
                processed_rows += len(batch_data)
                logger.info(f"表 {table_name} 已同步 {processed_rows}/{total_rows} 行 ({processed_rows/total_rows*100:.2f}%)")
            
            logger.info(f"表 {table_name} 增量数据同步完成")
            return True
        except Exception as e:
            logger.error(f"同步表 {table_name} 数据失败: {str(e)}")
            return False
    
    def get_max_id(self, table_name: str, primary_key: str, connection) -> Optional[Any]:
        """获取表中主键的最大值"""
        try:
            with connection.cursor() as cursor:
                # 检查表是否存在
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if cursor.fetchone() is None:
                    return None
                
                # 获取最大ID
                cursor.execute(f"SELECT MAX(`{primary_key}`) as max_id FROM `{table_name}`")
                result = cursor.fetchone()
                return result['max_id'] if result and result['max_id'] is not None else None
        except Exception as e:
            logger.error(f"获取表 {table_name} 最大ID失败: {str(e)}")
            return None
    
    def sync_table_data_full(self, table_name: str, batch_size: int = 20000) -> bool:
        """全量同步表数据（原有的同步逻辑）"""
        try:
            # 获取行数
            total_rows = self.count_table_rows(table_name)
            logger.info(f"表 {table_name} 共有 {total_rows} 行数据需要全量同步")
            
            if total_rows == 0:
                logger.info(f"表 {table_name} 没有数据，跳过数据同步")
                return True
            
            # 获取表的列
            with self.source_conn.cursor() as cursor:
                cursor.execute(f"SHOW COLUMNS FROM `{table_name}`")
                columns = [row['Field'] for row in cursor.fetchall()]
            
            columns_str = ", ".join([f"`{col}`" for col in columns])
            placeholders = ", ".join(["%s" for _ in columns])
            
            # 使用INSERT ... ON DUPLICATE KEY UPDATE语句处理冲突
            insert_sql = f"INSERT INTO `{table_name}` ({columns_str}) VALUES ({placeholders}) ON DUPLICATE KEY UPDATE "
            update_parts = [f"`{col}` = VALUES(`{col}`)" for col in columns]
            insert_sql += ", ".join(update_parts)
            
            # 分批同步数据
            offset = 0
            while offset < total_rows:
                with self.source_conn.cursor() as source_cursor:
                    source_cursor.execute(f"SELECT * FROM `{table_name}` LIMIT {offset}, {batch_size}")
                    batch_data = source_cursor.fetchall()
                    
                if not batch_data:
                    break
                
                # 准备插入数据
                values = []
                for row in batch_data:
                    row_values = [row[col] for col in columns]
                    values.append(row_values)
                
                # 执行批量插入
                with self.target_conn.cursor() as target_cursor:
                    target_cursor.executemany(insert_sql, values)
                self.target_conn.commit()
                
                offset += batch_size
                logger.info(f"表 {table_name} 已全量同步 {min(offset, total_rows)}/{total_rows} 行 ({min(offset, total_rows)/total_rows*100:.2f}%)")
            
            logger.info(f"表 {table_name} 全量数据同步完成")
            return True
        except Exception as e:
            logger.error(f"全量同步表 {table_name} 数据失败: {str(e)}")
            return False
    
    def sync_single_table(self, table_name: str) -> bool:
        """同步单个表结构和数据"""
        try:
            logger.info(f"开始同步表: {table_name}")
            
            # 获取表结构
            create_stmt = self.get_table_structure(table_name)
            
            # 在目标数据库中创建表
            if not self.create_table_in_target(table_name, create_stmt):
                return False
            
            # 同步表数据
            if not self.sync_table_data(table_name):
                return False
            
            logger.info(f"表 {table_name} 同步成功")
            return True
        except Exception as e:
            logger.error(f"同步表 {table_name} 失败: {str(e)}")
            return False
    
    def sync_database(self):
        """同步整个数据库"""
        try:
            start_time = time.time()
            logger.info("开始数据库同步过程...")
            
            # 设置连接
            self.setup_connections()
            
            # 获取源数据库中的所有表
            tables = self.get_source_tables()
            
            # 输出同步模式
            if self.incremental_mode:
                logger.info("使用增量同步模式 (基于主键ID)")
            else:
                logger.info("使用全量同步模式")
            
            # 使用线程池并行同步表
            success_count = 0
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = {executor.submit(self.sync_single_table, table): table for table in tables}
                for future in concurrent.futures.as_completed(futures):
                    table = futures[future]
                    try:
                        if future.result():
                            success_count += 1
                    except Exception as e:
                        logger.error(f"同步表 {table} 时发生异常: {str(e)}")
            
            # 输出统计信息
            end_time = time.time()
            duration = end_time - start_time
            logger.info(f"数据库同步完成，耗时 {duration:.2f} 秒")
            logger.info(f"成功同步 {success_count}/{len(tables)} 张表")
        
        except Exception as e:
            logger.error(f"数据库同步过程中发生错误: {str(e)}")
        finally:
            # 关闭连接
            self.close_connections()

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='MySQL数据同步工具')
    parser.add_argument('-s', '--source', help='源数据库配置 (格式: user:password@host:port/database)')
    parser.add_argument('-d', '--target', help='目标数据库配置 (格式: user:password@host:port/database)')
    parser.add_argument('-w', '--workers', type=int, default=MAX_WORKERS, help=f'并发工作线程数 (默认: {MAX_WORKERS})')
    parser.add_argument('-i', '--incremental', action='store_true', help='使用增量同步模式 (基于主键ID)')
    return parser.parse_args()

def print_banner():
    """打印工具横幅"""
    banner = """
    ====================================================
    ||                                                ||
    ||            MySQL数据库同步工具                 ||
    ||                                                ||
    ====================================================
    """
    print(banner)

def main():
    """主函数"""
    print_banner()
    
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 创建源数据库配置
        if args.source:
            logger.info("使用命令行参数提供的源数据库配置")
            source_config = DatabaseConfig.from_string(args.source)
        else:
            logger.info("使用代码中定义的源数据库配置")
            source_config = DatabaseConfig.from_dict(SOURCE_DB_CONFIG)
        
        # 创建目标数据库配置
        if args.target:
            logger.info("使用命令行参数提供的目标数据库配置")
            target_config = DatabaseConfig.from_string(args.target)
        else:
            logger.info("使用代码中定义的目标数据库配置")
            target_config = DatabaseConfig.from_dict(TARGET_DB_CONFIG)
        
        # 创建并运行同步工具
        sync_tool = MySQLSyncTool(source_config, target_config)
        if args.workers != MAX_WORKERS:
            sync_tool.max_workers = args.workers
            logger.info(f"设置并发工作线程数为: {args.workers}")
        
        # 不再覆盖incremental_mode，因为已在类初始化时设置为True
        sync_tool.incremental_mode = args.incremental
        
        sync_tool.sync_database()
        
    except Exception as e:
        logger.error(f"程序执行过程中发生错误: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 