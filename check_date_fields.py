# encoding: utf-8
import pandas as pd
import pymysql
import sys

new_path = ['D:\\0.维护代码\\1.数据库更新维护', ]
for path in new_path:
    if path not in sys.path:
        sys.path.append(path)

from cusreader_database_maintain import wind_remote_fun, get_all_table_names


def get_date_related_fields():
    """检查所有表，找出和日期相关的字段，并汇总去重"""
    db = wind_remote_fun()
    cursor = db.cursor()
    
    # 获取所有表名
    all_tables = get_all_table_names(db)
    print(f"总共找到 {len(all_tables)} 个表")
    
    date_fields = []
    date_field_types = ['DATE', 'DATETIME', 'TIMESTAMP']
    tables_without_entrytime = []  # 存储没有entrytime字段的表
    
    for table_name in all_tables:
        try:
            # 获取表结构
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            
            has_entrytime = False  # 标记是否有entrytime字段
            
            # 遍历所有列，检查是否为日期类型或名称包含日期相关关键词
            for column in columns:
                field_name = column[0]
                field_type = column[1].upper()
                
                # 检查是否有entrytime字段
                if field_name.upper() == 'ENTRYTIME':
                    has_entrytime = True
                
                # 检查字段类型是否为日期类型
                is_date_type = any(date_type in field_type for date_type in date_field_types)
                
                # 检查字段名是否包含日期相关关键词
                date_keywords = ['DATE', 'DAY', 'MONTH', 'YEAR', 'TIME', 'PERIOD', 'QUARTER', 'WEEK']
                is_date_name = any(keyword in field_name.upper() for keyword in date_keywords)
                
                if is_date_type or is_date_name:
                    date_fields.append({
                        'table_name': table_name,
                        'field_name': field_name,
                        'field_type': field_type,
                        'is_date_type': is_date_type,
                        'is_date_name': is_date_name
                    })
                    print(f"表 {table_name} 中找到日期相关字段: {field_name} ({field_type})")
            
            # 如果表没有entrytime字段，添加到列表
            if not has_entrytime:
                tables_without_entrytime.append(table_name)
                print(f"表 {table_name} 没有entrytime字段")
                
        except Exception as e:
            print(f"处理表 {table_name} 时出错: {e}")
    
    # 创建DataFrame并去重
    if date_fields:
        df = pd.DataFrame(date_fields)
        
        # 获取唯一的字段名
        unique_fields = df[['field_name', 'field_type']].drop_duplicates()
        print("\n所有唯一的日期相关字段:")
        print(unique_fields)
        
        # 统计每个字段在多少表中出现
        field_counts = df['field_name'].value_counts()
        print("\n日期字段出现次数统计:")
        print(field_counts)
        
        # 保存结果到文件
        df.to_csv('date_fields_summary.csv', index=False, encoding='utf-8-sig')
        unique_fields.to_csv('unique_date_fields.csv', index=False, encoding='utf-8-sig')
        
        print(f"\n总共发现 {len(date_fields)} 个日期相关字段记录，共 {len(unique_fields)} 个唯一字段")
        print("详细结果已保存到 date_fields_summary.csv 和 unique_date_fields.csv")
    else:
        print("未找到任何日期相关字段")
    
    # 保存没有entrytime字段的表清单
    if tables_without_entrytime:
        with open('tables_without_entrytime.txt', 'w', encoding='utf-8') as f:
            for table in tables_without_entrytime:
                f.write(f"{table}\n")
        print(f"\n发现 {len(tables_without_entrytime)} 个表没有entrytime字段")
        print("没有entrytime字段的表清单已保存到 tables_without_entrytime.txt")
    else:
        print("\n所有表都有entrytime字段")
    
    cursor.close()
    db.close()


def check_tables_without_field(field_name='ENTRYTIME'):
    """检查哪些表没有指定的字段(默认为ENTRYTIME)"""
    db = wind_remote_fun()
    cursor = db.cursor()
    
    # 获取所有表名
    all_tables = get_all_table_names(db)
    print(f"总共找到 {len(all_tables)} 个表")
    
    tables_without_field = []
    
    for table_name in all_tables:
        try:
            # 获取表结构
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            
            # 检查是否有指定字段
            field_exists = False
            for column in columns:
                if column[0].upper() == field_name.upper():
                    field_exists = True
                    break
            
            if not field_exists:
                tables_without_field.append(table_name)
                print(f"表 {table_name} 没有 {field_name} 字段")
                
        except Exception as e:
            print(f"处理表 {table_name} 时出错: {e}")
    
    # 保存没有指定字段的表清单
    if tables_without_field:
        output_file = f'tables_without_{field_name.lower()}.txt'
        with open(output_file, 'w', encoding='utf-8') as f:
            for table in tables_without_field:
                f.write(f"{table}\n")
        print(f"\n发现 {len(tables_without_field)} 个表没有 {field_name} 字段")
        print(f"没有 {field_name} 字段的表清单已保存到 {output_file}")
    else:
        print(f"\n所有表都有 {field_name} 字段")
    
    cursor.close()
    db.close()
    
    return tables_without_field


if __name__ == '__main__':
    action = input("请选择操作：\n1. 检查所有日期相关字段\n2. 检查没有ENTRYTIME字段的表\n3. 两项都执行\n请输入数字(1/2/3): ")
    
    if action == '1':
        get_date_related_fields()
    elif action == '2':
        field_name = input("请输入要检查的字段名(默认为ENTRYTIME): ") or "ENTRYTIME"
        check_tables_without_field(field_name)
    else:  # 默认执行所有
        get_date_related_fields()
        print("\n" + "="*50 + "\n")
        check_tables_without_field('ENTRYTIME') 