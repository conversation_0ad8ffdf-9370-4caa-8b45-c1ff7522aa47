# encoding: utf-8
import pandas as pd
import pymysql
import datetime
import sys
import json

new_path = ['D:\\0.维护代码\\1.数据库更新维护', ]
for path in new_path:
    if path not in sys.path:
        sys.path.append(path)


def wind_remote_fun():
    db_info = {
        'host': '*************',
        'port': 3306,
        'user': 'inforesdep01',
        'password': 'tfyfInfo@1602',
        'db': 'wind',
        'charset': 'utf8'
    }
    return pymysql.connect(**db_info)


def db_local_fun():
    db_info = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'db': 'wind',
        'charset': 'utf8'
    }
    return pymysql.connect(**db_info)


def convert_to_mysql_type(pymysql_type):
    map_dict = {
        pymysql.constants.FIELD_TYPE.VARCHAR: 'VARCHAR',
        pymysql.constants.FIELD_TYPE.CHAR: 'CHAR',
        pymysql.constants.FIELD_TYPE.TINY: 'TINYINT',
        pymysql.constants.FIELD_TYPE.LONG: 'INT',
        pymysql.constants.FIELD_TYPE.FLOAT: 'FLOAT',
        pymysql.constants.FIELD_TYPE.DOUBLE: 'DOUBLE',
        pymysql.constants.FIELD_TYPE.DECIMAL: 'DECIMAL',
        pymysql.constants.FIELD_TYPE.NEWDECIMAL: 'DECIMAL',
        pymysql.constants.FIELD_TYPE.LONGLONG: 'BIGINT',
        pymysql.constants.FIELD_TYPE.DATE: 'DATE',
        pymysql.constants.FIELD_TYPE.TIME: 'TIME',
        pymysql.constants.FIELD_TYPE.DATETIME: 'DATETIME',
        pymysql.constants.FIELD_TYPE.TIMESTAMP: 'TIMESTAMP',
        pymysql.constants.FIELD_TYPE.LONG_BLOB: 'LONGBLOB',
        pymysql.constants.FIELD_TYPE.MEDIUM_BLOB: 'MEDIUMBLOB',
        pymysql.constants.FIELD_TYPE.BLOB: 'LONGTEXT',
        pymysql.constants.FIELD_TYPE.TINY_BLOB: 'TINYBLOB',
        pymysql.constants.FIELD_TYPE.STRING: 'CHAR',
        pymysql.constants.FIELD_TYPE.JSON: 'JSON'
    }
    return map_dict.get(pymysql_type, 'VARCHAR')


def get_all_table_names(connector):
    cur = connector.cursor()
    cur.execute("SHOW TABLES")
    results = cur.fetchall()
    tables = [result[0] for result in results]
    return tables


def creat_new_table_frame(table_name):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()
    cursor_remote.execute(f"SELECT * FROM {table_name} LIMIT 0")
    desc = cursor_remote.description
    sql_create_table = ""
    for column in desc:
        field_name = column[0]
        field_type = column[1]
        internal_size = column[3]
        scale = column[5]
        null_ok = column[6]
        type = convert_to_mysql_type(field_type)
        if type in ["VARCHAR"]:
            type = type + f"({internal_size})"
        if type in ["DECIMAL"]:
            type = type + f"({internal_size},{scale})"
        if null_ok == False:
            type += " PRIMARY KEY"
        sql_create_table += f"`{field_name}`" + " " + type + ", "

    sql_create_table = sql_create_table[:-2]
    sql_create_table = f"CREATE TABLE {table_name} ({sql_create_table})"
    # 创建新表
    cursor_local.execute(sql_create_table)
    db_local.commit()


def contains_substring(A, B):
    return any(substr in A for substr in B)


def get_table_size(table_name):
    cursor_remote = wind_remote_fun().cursor()
    sql = f"SELECT round(((data_length + index_length) / 1024 / 1024), 2) `Size in MB` FROM information_schema.TABLES WHERE table_schema = 'wind' AND table_name = '{table_name}'"
    cursor_remote.execute(sql)
    result = cursor_remote.fetchone()
    return result[0]


def get_table_count(connect, table_name):
    cursor = connect.cursor()
    # sql = f"SELECT COUNT(*) FROM {table_name}"
    try:
        sql = f"SELECT COUNT(OBJECT_ID) FROM {table_name}"
        cursor.execute(sql)
        result = cursor.fetchone()
        return result[0]
    except Exception as e:
        print(f"Error occurred: {e}. Switching to temporary method...")
        sql = f"SELECT COUNT(*) FROM {table_name}"
        cursor.execute(sql)
        result = cursor.fetchone()
        return result[0]


def get_table_opdate_range(connection, table_name):
    with connection.cursor() as cur:
        sql = f"SELECT MIN(OPDATE), MAX(OPDATE) FROM {table_name}"
        cur.execute(sql)
        result = cur.fetchone()
        return result[0], result[1]


def get_added_data(table_name, opdate_max):
    cur = wind_remote_fun().cursor()
    cur.execute(f"SELECT * FROM {table_name} WHERE OPDATE > %s", (opdate_max,))
    results = cur.fetchall()
    return results, cur.description


def _insert_data(cursor, table_name, data, columns):
    try:
        column_sql = f'{",".join(columns)}'
        placeholders = ', '.join(['%s'] * len(columns))
        query = f"INSERT INTO {table_name} ({column_sql}) VALUES ({placeholders})"
        cursor.executemany(query, data)
    except Exception as e:
        print(f"Error occurred: {e}. Switching to temporary table method...")
        cursor.execute("DROP TEMPORARY TABLE IF EXISTS temp_table")
        cursor.execute(f"CREATE TEMPORARY TABLE temp_table LIKE {table_name}")
        query = f"INSERT INTO temp_table ({column_sql}) VALUES ({placeholders})"
        cursor.executemany(query, data)
        cursor.execute(f"DELETE main FROM {table_name} main JOIN temp_table temp ON main.OBJECT_ID = temp.OBJECT_ID")
        cursor.execute(f"INSERT INTO {table_name} SELECT * FROM temp_table")


def _delete_by_object_id(cursor, table_name, object_ids, batch_size=10000):
    for start_idx in range(0, len(object_ids), batch_size):
        batch_ids = object_ids[start_idx:start_idx + batch_size]
        placeholders = ', '.join(['%s'] * len(batch_ids))
        sql_delete = f"DELETE FROM {table_name} WHERE OBJECT_ID IN ({placeholders})"
        cursor.execute(sql_delete, batch_ids)
        print(f"Deleted {len(batch_ids)} rows from {table_name}.")
        print(f"Deleted ids: {batch_ids}")



def write_new_table_all_data(table_name):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()
    sql_fetch = f"SELECT * FROM {table_name}"
    cursor_remote.execute(sql_fetch)
    data = cursor_remote.fetchall()
    desc = cursor_remote.description
    columns = [column[0] for column in desc]

    _insert_data(cursor_local, table_name, data, columns)
    db_local.commit()


def write_new_table_separately(table_name, N=1):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()

    # 获取OPDATE的范围
    min_opdate, max_opdate = get_table_opdate_range(wind_remote_fun(), table_name)
    min_opdate = min_opdate.date()
    max_opdate = (max_opdate + datetime.timedelta(days=1)).date()
    if max_opdate < pd.Timestamp('2022-01-01').date():
        print(f"数据异常，{table_name}的最大日期为{max_opdate}")
        return 'old'
    current_opdate = min_opdate

    desc = None
    columns = None
    while current_opdate < max_opdate:
        next_opdate = current_opdate + datetime.timedelta(days=N)

        # 根据日期范围查询数据
        sql_fetch = f"SELECT * FROM {table_name} WHERE OPDATE >= %s AND OPDATE < %s"
        cursor_remote.execute(sql_fetch, (current_opdate, next_opdate))
        data = cursor_remote.fetchall()

        if not data:  # 如果没有数据，跳过这个日期范围
            current_opdate = next_opdate
            continue

        if desc is None:
            desc = cursor_remote.description
            columns = [column[0] for column in desc]

        _insert_data(cursor_local, table_name, data, columns)
        db_local.commit()

        print(f"已经写入{current_opdate}到{next_opdate}的数据: {len(data)}条")
        current_opdate = next_opdate


def update_table(table_name, N=2):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()

    # 获取OPDATE的范围
    min_opdate_local, max_opdate_local = get_table_opdate_range(db_local, table_name)
    min_opdate_remote, max_opdate_remote = get_table_opdate_range(wind_remote_fun(), table_name)
    if max_opdate_remote < pd.Timestamp('2023-01-01'):
        print(f"数据异常，{table_name}的最大日期为{max_opdate_remote}")
        return 'old'

    current_opdate = max_opdate_local

    desc = None
    columns = None
    while current_opdate < max_opdate_remote:
        next_opdate = current_opdate + datetime.timedelta(days=N)

        # 根据日期范围查询数据
        sql_fetch = f"SELECT * FROM {table_name} WHERE OPDATE > %s AND OPDATE < %s"
        cursor_remote.execute(sql_fetch, (current_opdate, next_opdate))
        data = cursor_remote.fetchall()

        if not data:  # 如果没有数据，跳过这个日期范围
            current_opdate = next_opdate
            continue

        if desc is None:
            desc = cursor_remote.description
            columns = [column[0] for column in desc]

        _insert_data(cursor_local, table_name, data, columns)
        db_local.commit()

        print(f"已经写入{current_opdate}到{next_opdate}的数据: {len(data)}条")
        current_opdate = next_opdate


def compare_object_id(table_name):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()

    # 获取远程和本地的所有OBJECT_ID
    cursor_remote.execute(f"SELECT OBJECT_ID FROM {table_name}")
    object_ids_remote = set([row[0] for row in cursor_remote.fetchall()])

    cursor_local.execute(f"SELECT OBJECT_ID FROM {table_name}")
    object_ids_local = set([row[0] for row in cursor_local.fetchall()])

    # 获取本地有但是远程没有的OBJECT_ID
    unused_ids = list(object_ids_local - object_ids_remote)
    print(f"本地有但是远程没有的OBJECT_ID的数量: {len(unused_ids)}")
    # print(f"本地有但是远程没有的OBJECT_ID: {unused_ids}")

    # 获取远程有但是本地没有的OBJECT_ID
    missing_ids = list(object_ids_remote - object_ids_local)
    print(f"远程有但是本地没有的OBJECT_ID的数量: {len(missing_ids)}")
    # print(f"远程有但是本地没有的OBJECT_ID: {missing_ids}")

    # 判断object_ids_local和object_ids_remote是否每一个都相等
    if len(unused_ids) == 0 and len(missing_ids) == 0:
        print(f"本地和远程的OBJECT_ID完全一致！")
        return True
    else:
        print(f"本地和远程的OBJECT_ID不一致！")
        return False


def update_by_object_id(table_name, batch_size=10000):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()

    # 获取远程和本地的所有OBJECT_ID
    cursor_remote.execute(f"SELECT OBJECT_ID FROM {table_name}")
    object_ids_remote = set([row[0] for row in cursor_remote.fetchall()])

    cursor_local.execute(f"SELECT OBJECT_ID FROM {table_name}")
    object_ids_local = set([row[0] for row in cursor_local.fetchall()])

    # 获取本地有但是远程没有的OBJECT_ID
    unused_ids = list(object_ids_local - object_ids_remote)
    _delete_by_object_id(cursor_local, table_name, unused_ids)
    db_local.commit()

    # 获取缺失的OBJECT_ID
    missing_ids = list(object_ids_remote - object_ids_local)
    if not missing_ids:
        print(f"OBJECT_ID全部一致！{table_name}已经是最新的了！")
    else:
        print(f"缺失的OBJECT_ID的数量: {len(missing_ids)}")
        desc = None
        columns = None

        # 分批获取缺失的数据并写入
        for start_idx in range(0, len(missing_ids), batch_size):
            batch_ids = missing_ids[start_idx:start_idx + batch_size]

            # 根据当前批次的OBJECT_ID列表从远程数据库中查询数据
            placeholders = ', '.join(['%s'] * len(batch_ids))
            sql_fetch = f"SELECT * FROM {table_name} WHERE OBJECT_ID IN ({placeholders})"
            cursor_remote.execute(sql_fetch, batch_ids)
            data = cursor_remote.fetchall()

            if not data:  # 如果没有数据，跳过这个批次
                continue

            if desc is None:
                desc = cursor_remote.description
                columns = [column[0] for column in desc]

            _insert_data(cursor_local, table_name, data, columns)
            db_local.commit()

            print(f"已经写入缺失的OBJECT_ID的数据: {len(data)}条")


def update_with_opmode_1(table_name):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()

    cursor_remote.execute(f"SELECT * FROM {table_name} WHERE OPMODE = 1")
    data = cursor_remote.fetchall()
    desc = cursor_remote.description
    columns = [column[0] for column in desc]
    _insert_data(cursor_local, table_name, data, columns)
    db_local.commit()
    print(f"已经写入缺失的OPMODE=1的数据: {len(data)}条")


def read_table_info(*filenames):
    combined_df = pd.DataFrame()

    for filename in filenames:
        print(f"开始读取{filename}...")
        filepath = f"D:/0.维护代码/1.数据库更新维护/db_config_info/{filename}.txt"
        try:
            with open(filepath, 'r', encoding='utf-8') as file:
                content = json.load(file)
            df = pd.DataFrame(content['Content']['Data'])
            combined_df = pd.concat([combined_df, df], ignore_index=True)
        except:
            print(f"Error reading file: {filepath}")
            continue

        
    return combined_df


def update_table_main(remote_table_list, check_id=False):
    local_table_list = get_all_table_names(db_local_fun())
    for table_name in remote_table_list:
        print(f"开始更新{table_name}...")
        num_remote = get_table_count(wind_remote_fun(), table_name)
        # if num_remote > 1000e4:
        #     print(f'{table_name}数量:{num_remote}')
        #     print(f"{table_name}数量太大，先跳过！")
        #     continue

        try:
            if table_name.lower() not in local_table_list:
                print(f"本地没有{table_name}，开始创建...")
                creat_new_table_frame(table_name)
                print(f"创建{table_name}成功！")
                print(f"开始写入{table_name}...")
                num_remote = get_table_count(wind_remote_fun(), table_name)
                if num_remote < 100000:
                    write_new_table_all_data(table_name)
                else:
                    print(f"分批写入{table_name}")
                    lb = write_new_table_separately(table_name)
                    if lb == 'old':
                        print(f"{table_name}数据太老，不获取！")
                        continue
                print(f"写入{table_name}成功！")
                print(f"开始检查{table_name}...")
                num_local = get_table_count(db_local_fun(), table_name)
                num_remote = get_table_count(wind_remote_fun(), table_name)
                print(f"远程数量：{num_remote}")
                print(f"本地数量：{num_local}")
                if num_remote != num_local:
                    print(f"{table_name}数量不相等！")
                    print(f"利用OBJECT_ID获取{table_name}")
                    update_by_object_id(table_name)
                    num_local = get_table_count(db_local_fun(), table_name)
                    print(f"本地数量：{num_local}")
                    if num_remote == num_local:
                        print(f"写入{table_name}成功！")
                    else:
                        print(f"数量依旧不相等,{table_name}更新失败！")
                else:
                    print(f"写入{table_name}成功！")
            else:
                print(f"本地已有{table_name}，开始更新...")
                num_remote = get_table_count(wind_remote_fun(), table_name)
                num_local = get_table_count(db_local_fun(), table_name)
                if num_local == 0:
                    if num_remote < 100000:
                        write_new_table_all_data(table_name)
                    else:
                        print(f"分批写入{table_name}")
                        write_new_table_separately(table_name)
                else:
                    lb = update_table(table_name)
                    if lb == 'old':
                        print(f"{table_name}数据太老，不更新！")
                        continue
                print(f"更新{table_name}成功！增加{num_remote - num_local}条数据！")
                print(f"开始检查{table_name}...")
                num_remote = get_table_count(wind_remote_fun(), table_name)
                num_local = get_table_count(db_local_fun(), table_name)
                print(f"远程数量：{num_remote}")
                print(f"本地数量：{num_local}")
                if num_remote != num_local:
                    print(f"{table_name}数量不相等！")
                    print(f"利用OPMODE更新{table_name}")
                    update_with_opmode_1(table_name)
                    num_local = get_table_count(db_local_fun(), table_name)
                    print(f"本地数量：{num_local}")
                    if num_remote == num_local:
                        if check_id:
                            print("检查OBJECT_ID...")
                            identical_id = compare_object_id(table_name)
                            if identical_id:
                                print("OBJECT_ID完全一致！")
                                print(f"更新{table_name}成功！")
                            else:
                                print("OBJECT_ID不一致！")
                    else:
                        print(f"利用OBJECT_ID获取{table_name}")
                        update_by_object_id(table_name)
                        num_local = get_table_count(db_local_fun(), table_name)
                        print(f"本地数量：{num_local}")
                        if num_remote == num_local:
                            print(f"更新{table_name}成功！")
                        else:
                            print(f"数量依旧不相等,{table_name}更新失败！")
                else:
                    if check_id:
                        print("检查OBJECT_ID...")
                        identical_id = compare_object_id(table_name)
                        if identical_id:
                            print("OBJECT_ID完全一致！")
                            print(f"更新{table_name}成功！")
                        else:
                            print("OBJECT_ID不一致！")

        except Exception as e:
            print(f"{table_name}更新失败！")
            print(e)

        print(f"{table_name}更新完毕！")
        print("---------------------------------------------------")


# 1. 创建新表时需要指定id
# 2. 写入要保证数据格式一致
# 3. 要先从本地获取最新的更新日期，远程获取大于这个日期的数据，再写入本地
# 4. 写入本地时要注意id是否是重复的，如果重复，就不写入（或者删除旧的，写入新的）
# 5. 更新完后，要获取总体的数据个数据，如果远程和本地不一致，就是有问题的，要报错
# 6. 定时更新运行，每天晚上更新一次

def daily_update():
    remote_table_list = \
        read_table_info('中国A股-行情交易数据', '中国A股-行情衍生数据', '中国A股-基础信息', '中国A股-万得一致预测',
                        '中国A股-指数数据', '共同基金-市场表现', '共同基金-指数数据', '共同基金-投资组合',
                        '共同基金-基础信息', '中国A股-机构调研')['ID'].tolist()
    remote_table_list = \
        read_table_info('共同基金-投资组合', '共同基金-申购赎回', '共同基金-评级')['ID'].tolist()
    update_table_main(remote_table_list)


def daily_update_for_factor():
    remote_table_list = ['AShareEODDerivativeIndicator', 'AShareEODPrices', 'AShareFinancialIndicator', 'AShareTTMHis',
                         'AShareIncome', 'AShareDescription']
    update_table_main(remote_table_list)

    # for table_name in remote_table_list:
    #     # print(f"开始更新{table_name}...")
    #     # print(f"利用修改时间获取{table_name}")
    #     # update_table(table_name)
    #     # print(f"利用OBJECT_ID获取{table_name}")
    #     # update_by_object_id(table_name)
    #     print(f"{table_name}更新完毕！")
    #     print("---------------------------------------------------")

# if __name__ == '__main__':
#
#     daily_update_for_factor()
