{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "香港股票日行情[HKshareEODPrices]", "ID": "HKshareEODPrices", "HasAuth": true, "sortOrder": 0, "IsContract": true, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false, "IsRoot": false}, {"DataType": 1, "Text": "陆港通机构持股[SHSCmechanismownership]", "ID": "SHSCmechanismownership", "HasAuth": true, "sortOrder": 0, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "香港交易所交易日历[HKEXCalendar]", "ID": "HKEXCalendar", "HasAuth": true, "sortOrder": 1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false, "IsRoot": false}, {"DataType": 1, "Text": "香港股票停复牌信息[HKTransactionStatus]", "ID": "HKTransactionStatus", "HasAuth": true, "sortOrder": 2, "IsContract": true, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false, "IsRoot": false}, {"DataType": 1, "Text": "香港股票可卖空证券明细[HKStockShortSellingList]", "ID": "HKStockShortSellingList", "HasAuth": true, "sortOrder": 3, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "香港股票卖空成交量[HKShareShortSellingTurnover]", "ID": "HKShareShortSellingTurnover", "HasAuth": true, "sortOrder": 4, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "香港股票证券未平仓卖空量[HKShareUnsoldShortsale]", "ID": "HKShareUnsoldShortsale", "HasAuth": true, "sortOrder": 4, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "陆港通日交易统计[SHSCDailyStatistics]", "ID": "SHSCDailyStatistics", "HasAuth": true, "sortOrder": 6, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "陆港通日十大成交活跃股统计[SHSCTop10ActiveStocks]", "ID": "SHSCTop10ActiveStocks", "HasAuth": true, "sortOrder": 7, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false, "IsRoot": false}, {"DataType": 1, "Text": "陆港通通道持股数量统计(中央结算系统)[SHSCChannelholdings]", "ID": "SHSCChannelholdings", "HasAuth": true, "sortOrder": 8, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "陆港通卖空数据[SHSCShortselling]", "ID": "SHSCShortselling", "HasAuth": true, "sortOrder": 9, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "港股市场中介持股统计[HKShareAgencyHoldings]", "ID": "HKShareAgencyHoldings", "HasAuth": true, "sortOrder": 11, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "香港股票盘后盘口指标[HKShareAfterEODPIndicators]", "ID": "HKShareAfterEODPIndicators", "HasAuth": true, "sortOrder": 12, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}], "PageInfo": null}, "success": true}