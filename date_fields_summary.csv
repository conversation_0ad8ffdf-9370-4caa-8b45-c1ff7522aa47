﻿table_name,field_name,field_type,is_date_type,is_date_name
bas_comp_capstructure,declare_date,DATETIM<PERSON>,True,True
bas_comp_capstructure,chg_startdate,DATETIM<PERSON>,True,True
bas_comp_capstructure,chg_enddate,D<PERSON><PERSON><PERSON><PERSON>,True,True
bas_comp_capstructure,xr_xd_date,D<PERSON><PERSON>IM<PERSON>,True,True
bas_comp_capstructure,entrytime,DATETIME,True,True
bas_comp_capstructure,updatetime,DATETIME,True,True
bas_comp_information,found_date,DATETIME,True,True
bas_comp_information,entrytime,DATETIM<PERSON>,True,True
bas_comp_information,updatetime,DATETIME,True,True
bas_idx_information,publish_date,DATETIM<PERSON>,True,True
bas_idx_information,bench_date,DATETIME,True,True
bas_idx_information,change_period,VARCHAR(10),False,True
bas_idx_information,end_date,DATETIM<PERSON>,True,True
bas_idx_information,entrytime,DATET<PERSON><PERSON>,True,True
bas_idx_information,updatetime,<PERSON><PERSON><PERSON><PERSON><PERSON>,True,True
bas_stk_hisdistribution,declare_date,D<PERSON><PERSON>IM<PERSON>,True,True
bas_stk_hisdistribution,distri_year,INT(11),False,True
bas_stk_hisdistribution,distridate_type,VARCHAR(10),False,True
bas_stk_hisdistribution,capbase_date,DATETIME,True,True
bas_stk_hisdistribution,right_recorddate,DATETIME,True,True
bas_stk_hisdistribution,xr_xd_date,DATETIME,True,True
bas_stk_hisdistribution,last_tradedate,DATETIME,True,True
bas_stk_hisdistribution,cashdiv_startdate,DATETIME,True,True
bas_stk_hisdistribution,cashdiv_enddate,DATETIME,True,True
bas_stk_hisdistribution,stock_arrdate,DATETIME,True,True
bas_stk_hisdistribution,list_date,DATETIME,True,True
bas_stk_hisdistribution,buyback_startdate,DATETIME,True,True
bas_stk_hisdistribution,buyback_enddate,DATETIME,True,True
bas_stk_hisdistribution,ass_replacedate,DATETIME,True,True
bas_stk_hisdistribution,bdresol_declaredate,DATETIME,True,True
bas_stk_hisdistribution,entrytime,DATETIME,True,True
bas_stk_hisdistribution,updatetime,DATETIME,True,True
bas_stk_information,declare_date,DATETIME,True,True
bas_stk_information,list_date,DATETIME,True,True
bas_stk_information,delist_date,DATETIME,True,True
bas_stk_information,entrytime,DATETIME,True,True
bas_stk_information,updatetime,DATETIME,True,True
con_forecast_citic,con_date,DATE,True,True
con_forecast_citic,con_year,INT(11),False,True
con_forecast_citic,entrytime,DATETIME,True,True
con_forecast_citic,updatetime,DATETIME,True,True
con_forecast_idx,con_date,DATE,True,True
con_forecast_idx,con_year,INT(11),False,True
con_forecast_idx,entrytime,DATETIME,True,True
con_forecast_idx,updatetime,DATETIME,True,True
con_forecast_roll_citic,con_date,DATE,True,True
con_forecast_roll_citic,entrytime,DATETIME,True,True
con_forecast_roll_citic,updatetime,DATETIME,True,True
con_forecast_roll_idx,con_date,DATE,True,True
con_forecast_roll_idx,entrytime,DATETIME,True,True
con_forecast_roll_idx,updatetime,DATETIME,True,True
con_forecast_roll_stk,con_date,DATE,True,True
con_forecast_roll_stk,entrytime,DATETIME,True,True
con_forecast_roll_stk,updatetime,DATETIME,True,True
con_forecast_roll_sw,con_date,DATE,True,True
con_forecast_roll_sw,entrytime,DATETIME,True,True
con_forecast_roll_sw,updatetime,DATETIME,True,True
con_forecast_stk,con_date,DATE,True,True
con_forecast_stk,con_year,INT(11),False,True
con_forecast_stk,con_or_hisdate,DATE,True,True
con_forecast_stk,con_np_hisdate,DATE,True,True
con_forecast_stk,con_eps_hisdate,DATE,True,True
con_forecast_stk,entrytime,DATETIME,True,True
con_forecast_stk,updatetime,DATETIME,True,True
con_forecast_stk_eq,con_date,DATE,True,True
con_forecast_stk_eq,con_year,INT(11),False,True
con_forecast_stk_eq,con_or_hisdate,DATE,True,True
con_forecast_stk_eq,con_np_hisdate,DATE,True,True
con_forecast_stk_eq,con_eps_hisdate,DATE,True,True
con_forecast_stk_eq,entrytime,DATETIME,True,True
con_forecast_stk_eq,updatetime,DATETIME,True,True
con_forecast_stk_wgt,con_date,DATE,True,True
con_forecast_stk_wgt,con_year,INT(11),False,True
con_forecast_stk_wgt,con_or_hisdate,DATE,True,True
con_forecast_stk_wgt,con_np_hisdate,DATE,True,True
con_forecast_stk_wgt,con_eps_hisdate,DATE,True,True
con_forecast_stk_wgt,entrytime,DATETIME,True,True
con_forecast_stk_wgt,updatetime,DATETIME,True,True
con_forecast_sw,con_date,DATE,True,True
con_forecast_sw,con_year,INT(11),False,True
con_forecast_sw,entrytime,DATETIME,True,True
con_forecast_sw,updatetime,DATETIME,True,True
con_rating_stk,con_date,DATE,True,True
con_rating_stk,entrytime,DATETIME,True,True
con_rating_stk,updatetime,DATETIME,True,True
con_rating_stk_eq,con_date,DATE,True,True
con_rating_stk_eq,con_rating_hisdate,DATE,True,True
con_rating_stk_eq,entrytime,DATETIME,True,True
con_rating_stk_eq,updatetime,DATETIME,True,True
con_rating_stk_wgt,con_date,DATE,True,True
con_rating_stk_wgt,con_rating_hisdate,DATE,True,True
con_rating_stk_wgt,entrytime,DATETIME,True,True
con_rating_stk_wgt,updatetime,DATETIME,True,True
con_target_price_stk,con_date,DATE,True,True
con_target_price_stk,entrytime,DATETIME,True,True
con_target_price_stk,updatetime,DATETIME,True,True
con_target_price_stk_eq,con_date,DATE,True,True
con_target_price_stk_eq,con_target_price_hisdate,DATE,True,True
con_target_price_stk_eq,entrytime,DATETIME,True,True
con_target_price_stk_eq,updatetime,DATETIME,True,True
der_adjust_speed_cicc,con_date,DATE,True,True
der_adjust_speed_cicc,con_year,INT(11),False,True
der_adjust_speed_cicc,entrytime,DATETIME,True,True
der_adjust_speed_cicc,updatetime,DATETIME,True,True
der_adjust_speed_citic,con_date,DATE,True,True
der_adjust_speed_citic,con_year,INT(11),False,True
der_adjust_speed_citic,entrytime,DATETIME,True,True
der_adjust_speed_citic,updatetime,DATETIME,True,True
der_adjust_speed_gics,con_date,DATE,True,True
der_adjust_speed_gics,con_year,INT(11),False,True
der_adjust_speed_gics,entrytime,DATETIME,True,True
der_adjust_speed_gics,updatetime,DATETIME,True,True
der_adjust_speed_idx,con_date,DATE,True,True
der_adjust_speed_idx,con_year,INT(11),False,True
der_adjust_speed_idx,entrytime,DATETIME,True,True
der_adjust_speed_idx,updatetime,DATETIME,True,True
der_adjust_speed_sw,con_date,DATE,True,True
der_adjust_speed_sw,con_year,INT(11),False,True
der_adjust_speed_sw,entrytime,DATETIME,True,True
der_adjust_speed_sw,updatetime,DATETIME,True,True
der_author_acc_eval,report_year,INT(11),False,True
der_author_acc_eval,entrytime,DATETIME,True,True
der_author_acc_eval,updatetime,DATETIME,True,True
der_author_honor,begin_date,DATE,True,True
der_author_honor,end_date,DATE,True,True
der_author_honor,entrytime,DATETIME,True,True
der_author_honor,updatetime,DATETIME,True,True
der_author_rating_eval,report_year,INT(11),False,True
der_author_rating_eval,entrytime,DATETIME,True,True
der_author_rating_eval,updatetime,DATETIME,True,True
der_con_dev_cicc,con_date,DATE,True,True
der_con_dev_cicc,con_year,INT(11),False,True
der_con_dev_cicc,entrytime,DATETIME,True,True
der_con_dev_cicc,updatetime,DATETIME,True,True
der_con_dev_citic,con_date,DATE,True,True
der_con_dev_citic,con_year,INT(11),False,True
der_con_dev_citic,entrytime,DATETIME,True,True
der_con_dev_citic,updatetime,DATETIME,True,True
der_con_dev_gics,con_date,DATE,True,True
der_con_dev_gics,con_year,INT(11),False,True
der_con_dev_gics,entrytime,DATETIME,True,True
der_con_dev_gics,updatetime,DATETIME,True,True
der_con_dev_idx,con_date,DATE,True,True
der_con_dev_idx,con_year,INT(11),False,True
der_con_dev_idx,entrytime,DATETIME,True,True
der_con_dev_idx,updatetime,DATETIME,True,True
der_con_dev_roll_cicc,con_date,DATE,True,True
der_con_dev_roll_cicc,entrytime,DATETIME,True,True
der_con_dev_roll_cicc,updatetime,DATETIME,True,True
der_con_dev_roll_citic,con_date,DATE,True,True
der_con_dev_roll_citic,entrytime,DATETIME,True,True
der_con_dev_roll_citic,updatetime,DATETIME,True,True
der_con_dev_roll_gics,con_date,DATE,True,True
der_con_dev_roll_gics,entrytime,DATETIME,True,True
der_con_dev_roll_gics,updatetime,DATETIME,True,True
der_con_dev_roll_idx,con_date,DATE,True,True
der_con_dev_roll_idx,entrytime,DATETIME,True,True
der_con_dev_roll_idx,updatetime,DATETIME,True,True
der_con_dev_roll_stk,con_date,DATE,True,True
der_con_dev_roll_stk,entrytime,DATETIME,True,True
der_con_dev_roll_stk,updatetime,DATETIME,True,True
der_con_dev_roll_sw,con_date,DATE,True,True
der_con_dev_roll_sw,entrytime,DATETIME,True,True
der_con_dev_roll_sw,updatetime,DATETIME,True,True
der_con_dev_stk,con_date,DATE,True,True
der_con_dev_stk,con_year,INT(11),False,True
der_con_dev_stk,entrytime,DATETIME,True,True
der_con_dev_stk,updatetime,DATETIME,True,True
der_con_dev_sw,con_date,DATE,True,True
der_con_dev_sw,con_year,INT(11),False,True
der_con_dev_sw,entrytime,DATETIME,True,True
der_con_dev_sw,updatetime,DATETIME,True,True
der_conf_cicc,con_date,DATE,True,True
der_conf_cicc,con_year,INT(11),False,True
der_conf_cicc,entrytime,DATETIME,True,True
der_conf_cicc,updatetime,DATETIME,True,True
der_conf_citic,con_date,DATE,True,True
der_conf_citic,con_year,INT(11),False,True
der_conf_citic,entrytime,DATETIME,True,True
der_conf_citic,updatetime,DATETIME,True,True
der_conf_gics,con_date,DATE,True,True
der_conf_gics,con_year,INT(11),False,True
der_conf_gics,entrytime,DATETIME,True,True
der_conf_gics,updatetime,DATETIME,True,True
der_conf_idx,con_date,DATE,True,True
der_conf_idx,con_year,INT(11),False,True
der_conf_idx,entrytime,DATETIME,True,True
der_conf_idx,updatetime,DATETIME,True,True
der_conf_stk,con_date,DATE,True,True
der_conf_stk,con_year,INT(11),False,True
der_conf_stk,entrytime,DATETIME,True,True
der_conf_stk,updatetime,DATETIME,True,True
der_conf_sw,con_date,DATE,True,True
der_conf_sw,con_year,INT(11),False,True
der_conf_sw,entrytime,DATETIME,True,True
der_conf_sw,updatetime,DATETIME,True,True
der_const_adjust_cicc,con_date,DATE,True,True
der_const_adjust_cicc,con_year,INT(11),False,True
der_const_adjust_cicc,entrytime,DATETIME,True,True
der_const_adjust_cicc,updatetime,DATETIME,True,True
der_const_adjust_citic,con_date,DATE,True,True
der_const_adjust_citic,con_year,INT(11),False,True
der_const_adjust_citic,entrytime,DATETIME,True,True
der_const_adjust_citic,updatetime,DATETIME,True,True
der_const_adjust_gics,con_date,DATE,True,True
der_const_adjust_gics,con_year,INT(11),False,True
der_const_adjust_gics,entrytime,DATETIME,True,True
der_const_adjust_gics,updatetime,DATETIME,True,True
der_const_adjust_idx,con_date,DATE,True,True
der_const_adjust_idx,con_year,INT(11),False,True
der_const_adjust_idx,entrytime,DATETIME,True,True
der_const_adjust_idx,updatetime,DATETIME,True,True
der_const_adjust_sw,con_date,DATE,True,True
der_const_adjust_sw,con_year,INT(11),False,True
der_const_adjust_sw,entrytime,DATETIME,True,True
der_const_adjust_sw,updatetime,DATETIME,True,True
der_core_author,report_year,INT(11),False,True
der_core_author,entrytime,DATETIME,True,True
der_core_author,updatetime,DATETIME,True,True
der_crystalball_author,REPORT_YEAR,INT(11),False,True
der_crystalball_author,ENTRYTIME,DATETIME,True,True
der_crystalball_author,UPDATETIME,DATETIME,True,True
der_diver_cicc,con_date,DATE,True,True
der_diver_cicc,con_year,INT(11),False,True
der_diver_cicc,entrytime,DATETIME,True,True
der_diver_cicc,updatetime,DATETIME,True,True
der_diver_citic,con_date,DATE,True,True
der_diver_citic,con_year,INT(11),False,True
der_diver_citic,entrytime,DATETIME,True,True
der_diver_citic,updatetime,DATETIME,True,True
der_diver_gics,con_date,DATE,True,True
der_diver_gics,con_year,INT(11),False,True
der_diver_gics,entrytime,DATETIME,True,True
der_diver_gics,updatetime,DATETIME,True,True
der_diver_idx,con_date,DATE,True,True
der_diver_idx,con_year,INT(11),False,True
der_diver_idx,entrytime,DATETIME,True,True
der_diver_idx,updatetime,DATETIME,True,True
der_diver_stk,con_date,DATE,True,True
der_diver_stk,con_year,INT(11),False,True
der_diver_stk,entrytime,DATETIME,True,True
der_diver_stk,updatetime,DATETIME,True,True
der_diver_sw,con_date,DATE,True,True
der_diver_sw,con_year,INT(11),False,True
der_diver_sw,entrytime,DATETIME,True,True
der_diver_sw,updatetime,DATETIME,True,True
der_excess_stock,report_year,INT(11),False,True
der_excess_stock,report_quarter,INT(11),False,True
der_excess_stock,declare_date,DATE,True,True
der_excess_stock,appraisal_date,DATE,True,True
der_excess_stock,entrytime,DATETIME,True,True
der_excess_stock,updatetime,DATETIME,True,True
der_focus_cicc,con_date,DATE,True,True
der_focus_cicc,entrytime,DATETIME,True,True
der_focus_cicc,updatetime,DATETIME,True,True
der_focus_citic,con_date,DATE,True,True
der_focus_citic,entrytime,DATETIME,True,True
der_focus_citic,updatetime,DATETIME,True,True
der_focus_gics,con_date,DATE,True,True
der_focus_gics,entrytime,DATETIME,True,True
der_focus_gics,updatetime,DATETIME,True,True
der_focus_idx,con_date,DATE,True,True
der_focus_idx,entrytime,DATETIME,True,True
der_focus_idx,updatetime,DATETIME,True,True
der_focus_stk,con_date,DATE,True,True
der_focus_stk,entrytime,DATETIME,True,True
der_focus_stk,updatetime,DATETIME,True,True
der_focus_sw,con_date,DATE,True,True
der_focus_sw,entrytime,DATETIME,True,True
der_focus_sw,updatetime,DATETIME,True,True
der_forecast_adjust_num,con_date,DATE,True,True
der_forecast_adjust_num,con_year,INT(11),False,True
der_forecast_adjust_num,entrytime,DATETIME,True,True
der_forecast_adjust_num,updatetime,DATETIME,True,True
der_forecast_rank,con_date,DATE,True,True
der_forecast_rank,con_year,INT(11),False,True
der_forecast_rank,entrytime,DATETIME,True,True
der_forecast_rank,updatetime,DATETIME,True,True
der_forecast_roll_rank,con_date,DATE,True,True
der_forecast_roll_rank,entrytime,DATETIME,True,True
der_forecast_roll_rank,updatetime,DATETIME,True,True
der_new_fortune_author,report_year,INT(11),False,True
der_new_fortune_author,entrytime,DATETIME,True,True
der_new_fortune_author,updatetime,DATETIME,True,True
der_organ_focus_cicc,con_date,DATE,True,True
der_organ_focus_cicc,entrytime,DATETIME,True,True
der_organ_focus_cicc,updatetime,DATETIME,True,True
der_organ_focus_citic,con_date,DATE,True,True
der_organ_focus_citic,entrytime,DATETIME,True,True
der_organ_focus_citic,updatetime,DATETIME,True,True
der_organ_focus_gics,con_date,DATE,True,True
der_organ_focus_gics,entrytime,DATETIME,True,True
der_organ_focus_gics,updatetime,DATETIME,True,True
der_organ_focus_idx,con_date,DATE,True,True
der_organ_focus_idx,entrytime,DATETIME,True,True
der_organ_focus_idx,updatetime,DATETIME,True,True
der_organ_focus_sw,con_date,DATE,True,True
der_organ_focus_sw,entrytime,DATETIME,True,True
der_organ_focus_sw,updatetime,DATETIME,True,True
der_organ_forecast,con_date,DATE,True,True
der_organ_forecast,con_year,INT(11),False,True
der_organ_forecast,entrytime,DATETIME,True,True
der_organ_forecast,updatetime,DATETIME,True,True
der_prob_excess_stock,report_year,INT(11),False,True
der_prob_excess_stock,report_quarter,INT(11),False,True
der_prob_excess_stock,declare_date,DATE,True,True
der_prob_excess_stock,entrytime,DATETIME,True,True
der_prob_excess_stock,updatetime,DATETIME,True,True
der_rating_adjust_num,con_date,DATE,True,True
der_rating_adjust_num,entrytime,DATETIME,True,True
der_rating_adjust_num,updatetime,DATETIME,True,True
der_rating_strength_cicc,con_date,DATE,True,True
der_rating_strength_cicc,entrytime,DATETIME,True,True
der_rating_strength_cicc,updatetime,DATETIME,True,True
der_rating_strength_citic,con_date,DATE,True,True
der_rating_strength_citic,entrytime,DATETIME,True,True
der_rating_strength_citic,updatetime,DATETIME,True,True
der_rating_strength_gics,con_date,DATE,True,True
der_rating_strength_gics,entrytime,DATETIME,True,True
der_rating_strength_gics,updatetime,DATETIME,True,True
der_rating_strength_idx,con_date,DATE,True,True
der_rating_strength_idx,entrytime,DATETIME,True,True
der_rating_strength_idx,updatetime,DATETIME,True,True
der_rating_strength_rank,con_date,DATE,True,True
der_rating_strength_rank,entrytime,DATETIME,True,True
der_rating_strength_rank,updatetime,DATETIME,True,True
der_rating_strength_sw,con_date,DATE,True,True
der_rating_strength_sw,entrytime,DATETIME,True,True
der_rating_strength_sw,updatetime,DATETIME,True,True
der_report_extre,con_date,DATE,True,True
der_report_extre,con_year,INT(11),False,True
der_report_extre,entrytime,DATETIME,True,True
der_report_extre,updatetime,DATETIME,True,True
der_report_num,con_date,DATE,True,True
der_report_num,entrytime,DATETIME,True,True
der_report_num,updatetime,DATETIME,True,True
fin_balance_sheet_bank,report_year,INT(11),False,True
fin_balance_sheet_bank,report_quarter,INT(11),False,True
fin_balance_sheet_bank,declare_date,DATETIME,True,True
fin_balance_sheet_bank,entrytime,DATETIME,True,True
fin_balance_sheet_bank,updatetime,DATETIME,True,True
fin_balance_sheet_gen,report_year,INT(11),False,True
fin_balance_sheet_gen,report_quarter,INT(11),False,True
fin_balance_sheet_gen,declare_date,DATETIME,True,True
fin_balance_sheet_gen,entrytime,DATETIME,True,True
fin_balance_sheet_gen,updatetime,DATETIME,True,True
fin_balance_sheet_insur,report_year,INT(11),False,True
fin_balance_sheet_insur,report_quarter,INT(11),False,True
fin_balance_sheet_insur,declare_date,DATETIME,True,True
fin_balance_sheet_insur,entrytime,DATETIME,True,True
fin_balance_sheet_insur,updatetime,DATETIME,True,True
fin_balance_sheet_sec,report_year,INT(11),False,True
fin_balance_sheet_sec,report_quarter,INT(11),False,True
fin_balance_sheet_sec,declare_date,DATETIME,True,True
fin_balance_sheet_sec,entrytime,DATETIME,True,True
fin_balance_sheet_sec,updatetime,DATETIME,True,True
fin_balance_sheet_single,REPORT_YEAR,INT(11),False,True
fin_balance_sheet_single,REPORT_QUARTER,VARCHAR(10),False,True
fin_balance_sheet_single,DECLARE_DATE,DATETIME,True,True
fin_balance_sheet_single,ENTRYTIME,DATETIME,True,True
fin_balance_sheet_single,UPDATETIME,DATETIME,True,True
fin_balance_sheet_ttm,REPORT_YEAR,INT(11),False,True
fin_balance_sheet_ttm,REPORT_QUARTER,VARCHAR(10),False,True
fin_balance_sheet_ttm,BEGIN_DATE,DATETIME,True,True
fin_balance_sheet_ttm,END_DATE,DATETIME,True,True
fin_balance_sheet_ttm,ENTRYTIME,DATETIME,True,True
fin_balance_sheet_ttm,UPDATETIME,DATETIME,True,True
fin_cash_flow_bank,report_year,INT(11),False,True
fin_cash_flow_bank,report_quarter,INT(11),False,True
fin_cash_flow_bank,declare_date,DATETIME,True,True
fin_cash_flow_bank,entrytime,DATETIME,True,True
fin_cash_flow_bank,updatetime,DATETIME,True,True
fin_cash_flow_gen,report_year,INT(11),False,True
fin_cash_flow_gen,report_quarter,INT(11),False,True
fin_cash_flow_gen,declare_date,DATETIME,True,True
fin_cash_flow_gen,entrytime,DATETIME,True,True
fin_cash_flow_gen,updatetime,DATETIME,True,True
fin_cash_flow_insur,report_year,INT(11),False,True
fin_cash_flow_insur,report_quarter,INT(11),False,True
fin_cash_flow_insur,declare_date,DATETIME,True,True
fin_cash_flow_insur,entrytime,DATETIME,True,True
fin_cash_flow_insur,updatetime,DATETIME,True,True
fin_cash_flow_sec,report_year,INT(11),False,True
fin_cash_flow_sec,report_quarter,INT(11),False,True
fin_cash_flow_sec,declare_date,DATETIME,True,True
fin_cash_flow_sec,entrytime,DATETIME,True,True
fin_cash_flow_sec,updatetime,DATETIME,True,True
fin_cash_flow_single,REPORT_YEAR,INT(11),False,True
fin_cash_flow_single,REPORT_QUARTER,INT(11),False,True
fin_cash_flow_single,DECLARE_DATE,DATETIME,True,True
fin_cash_flow_single,ENTRYTIME,DATETIME,True,True
fin_cash_flow_single,UPDATETIME,DATETIME,True,True
fin_cash_flow_ttm,REPORT_YEAR,INT(11),False,True
fin_cash_flow_ttm,REPORT_QUARTER,INT(11),False,True
fin_cash_flow_ttm,BEGIN_DATE,DATETIME,True,True
fin_cash_flow_ttm,END_DATE,DATETIME,True,True
fin_cash_flow_ttm,ENTRYTIME,DATETIME,True,True
fin_cash_flow_ttm,UPDATETIME,DATETIME,True,True
fin_income_bank,report_year,INT(11),False,True
fin_income_bank,report_quarter,INT(11),False,True
fin_income_bank,declare_date,DATETIME,True,True
fin_income_bank,entrytime,DATETIME,True,True
fin_income_bank,updatetime,DATETIME,True,True
fin_income_gen,report_year,INT(11),False,True
fin_income_gen,report_quarter,INT(11),False,True
fin_income_gen,declare_date,DATETIME,True,True
fin_income_gen,entrytime,DATETIME,True,True
fin_income_gen,updatetime,DATETIME,True,True
fin_income_insur,report_year,INT(11),False,True
fin_income_insur,report_quarter,INT(11),False,True
fin_income_insur,declare_date,DATETIME,True,True
fin_income_insur,entrytime,DATETIME,True,True
fin_income_insur,updatetime,DATETIME,True,True
fin_income_sec,report_year,INT(11),False,True
fin_income_sec,report_quarter,INT(11),False,True
fin_income_sec,declare_date,DATETIME,True,True
fin_income_sec,entrytime,DATETIME,True,True
fin_income_sec,updatetime,DATETIME,True,True
fin_income_single,REPORT_YEAR,INT(11),False,True
fin_income_single,REPORT_QUARTER,VARCHAR(10),False,True
fin_income_single,DECLARE_DATE,DATETIME,True,True
fin_income_single,ENTRYTIME,DATETIME,True,True
fin_income_single,UPDATETIME,DATETIME,True,True
fin_income_ttm,REPORT_YEAR,INT(11),False,True
fin_income_ttm,REPORT_QUARTER,INT(11),False,True
fin_income_ttm,BEGIN_DATE,DATETIME,True,True
fin_income_ttm,END_DATE,DATETIME,True,True
fin_income_ttm,ENTRYTIME,DATETIME,True,True
fin_income_ttm,UPDATETIME,DATETIME,True,True
fin_main_indicator,report_year,INT(11),False,True
fin_main_indicator,report_quarter,INT(11),False,True
fin_main_indicator,declare_date,DATE,True,True
fin_main_indicator,entrytime,DATETIME,True,True
fin_main_indicator,updatetime,DATETIME,True,True
fin_main_ratio,report_year,INT(11),False,True
fin_main_ratio,report_quarter,INT(11),False,True
fin_main_ratio,declare_date,DATE,True,True
fin_main_ratio,entrytime,DATETIME,True,True
fin_main_ratio,updatetime,DATETIME,True,True
fin_performance_express,report_period,INT(11),False,True
fin_performance_express,report_year,INT(11),False,True
fin_performance_express,declare_date,DATETIME,True,True
fin_performance_express,entrytime,DATETIME,True,True
fin_performance_express,updatetime,DATETIME,True,True
fin_performance_forecast,report_year,INT(11),False,True
fin_performance_forecast,report_period,INT(11),False,True
fin_performance_forecast,declare_date,DATETIME,True,True
fin_performance_forecast,entrytime,DATETIME,True,True
fin_performance_forecast,updatetime,DATETIME,True,True
gs_rpt_goldstock,create_date,DATE,True,True
gs_rpt_goldstock,report_year,INT(11),False,True
gs_rpt_goldstock,report_period,VARCHAR(20),False,True
gs_rpt_goldstock,report_period_type,INT(11),False,True
gs_rpt_goldstock,entrytime,DATETIME,True,True
gs_rpt_goldstock,updatetime,DATETIME,True,True
qt_idx_constituents,into_date,DATETIME,True,True
qt_idx_constituents,out_date,DATETIME,True,True
qt_idx_constituents,first_entrytime,DATETIME,True,True
qt_idx_constituents,out_declaredate,DATETIME,True,True
qt_idx_constituents,entrytime,DATETIME,True,True
qt_idx_constituents,updatetime,DATETIME,True,True
qt_idx_daily,trade_date,DATETIME,True,True
qt_idx_daily,entrytime,DATETIME,True,True
qt_idx_daily,updatetime,DATETIME,True,True
qt_indus_categories,entrytime,DATETIME,True,True
qt_indus_categories,updatetime,DATETIME,True,True
qt_indus_constituents,into_date,DATE,True,True
qt_indus_constituents,out_date,DATE,True,True
qt_indus_constituents,entrytime,DATETIME,True,True
qt_indus_constituents,updatetime,DATETIME,True,True
qt_ntrade_date,PUBLISH_DATE,DATETIME,True,True
qt_ntrade_date,NTRADE_TIME,VARCHAR(10),False,True
qt_ntrade_date,NTRADE_STARTDATE,DATETIME,True,True
qt_ntrade_date,NTRADE_ENDDATE,DATETIME,True,True
qt_ntrade_date,RTRADE_DATE,DATETIME,True,True
qt_ntrade_date,ENTRYTIME,DATETIME,True,True
qt_ntrade_date,UPDATETIME,DATETIME,True,True
qt_stk_block,entrytime,DATETIME,True,True
qt_stk_block,updatetime,DATETIME,True,True
qt_stk_daily,trade_date,DATETIME,True,True
qt_stk_daily,entrytime,DATETIME,True,True
qt_stk_daily,updatetime,DATETIME,True,True
qt_sw_daily,trade_date,DATE,True,True
qt_sw_daily,entrytime,DATETIME,True,True
qt_sw_daily,updatetime,DATETIME,True,True
qt_sw_indus_his,BEGIN_DATE,DATE,True,True
qt_sw_indus_his,END_DATE,DATE,True,True
qt_sw_indus_his,ENTRY_TIME,DATETIME,True,True
qt_sw_indus_his,UPDATE_TIME,DATETIME,True,True
qt_trade_date,TRADE_DATE,DATETIME,True,True
qt_trade_date,IS_TRADE_DATE,INT(11),False,True
qt_trade_date,VTRADE_DATE,DATETIME,True,True
qt_trade_date,LTRADE_DATE,DATETIME,True,True
qt_trade_date,NTRADE_DATE,DATETIME,True,True
qt_trade_date,ENTRYTIME,DATETIME,True,True
qt_trade_date,UPDATETIME,DATETIME,True,True
rpt_author_information,entrytime,DATETIME,True,True
rpt_author_information,updatetime,DATETIME,True,True
rpt_earnings_adjust,report_year,INT(11),False,True
rpt_earnings_adjust,current_create_date,DATE,True,True
rpt_earnings_adjust,previous_create_date,DATE,True,True
rpt_earnings_adjust,entrytime,DATETIME,True,True
rpt_earnings_adjust,updatetime,DATETIME,True,True
rpt_forecast_bs_stk,create_date,DATE,True,True
rpt_forecast_bs_stk,report_year,INT(11),False,True
rpt_forecast_bs_stk,report_quarter,INT(11),False,True
rpt_forecast_bs_stk,entrytime,DATETIME,True,True
rpt_forecast_bs_stk,updatetime,DATETIME,True,True
rpt_forecast_cs_stk,create_date,DATE,True,True
rpt_forecast_cs_stk,report_year,INT(11),False,True
rpt_forecast_cs_stk,report_quarter,INT(11),False,True
rpt_forecast_cs_stk,entrytime,DATETIME,True,True
rpt_forecast_cs_stk,updatetime,DATETIME,True,True
rpt_forecast_is_stk,create_date,DATE,True,True
rpt_forecast_is_stk,report_year,INT(11),False,True
rpt_forecast_is_stk,report_quarter,INT(11),False,True
rpt_forecast_is_stk,entrytime,DATETIME,True,True
rpt_forecast_is_stk,updatetime,DATETIME,True,True
rpt_forecast_stk,create_date,DATE,True,True
rpt_forecast_stk,report_year,INT(11),False,True
rpt_forecast_stk,report_quarter,INT(11),False,True
rpt_forecast_stk,settlement_date,VARCHAR(200),False,True
rpt_forecast_stk,entrytime,DATETIME,True,True
rpt_forecast_stk,updatetime,DATETIME,True,True
rpt_forecast_stk_test,create_date,DATE,True,True
rpt_forecast_stk_test,report_year,INT(11),False,True
rpt_forecast_stk_test,report_quarter,INT(11),False,True
rpt_forecast_stk_test,settlement_date,VARCHAR(200),False,True
rpt_forecast_stk_test,entrytime,DATETIME,True,True
rpt_forecast_stk_test,updatetime,DATETIME,True,True
rpt_gogoal_rating,entrytime,DATETIME,True,True
rpt_gogoal_rating,updatetime,DATETIME,True,True
rpt_organ_information,entrytime,DATETIME,True,True
rpt_organ_information,updatetime,DATETIME,True,True
rpt_rating_adjust,current_create_date,DATE,True,True
rpt_rating_adjust,previous_create_date,DATE,True,True
rpt_rating_adjust,entrytime,DATETIME,True,True
rpt_rating_adjust,updatetime,DATETIME,True,True
rpt_rating_compare,entrytime,DATETIME,True,True
rpt_rating_compare,updatetime,DATETIME,True,True
rpt_report_author,entrytime,DATETIME,True,True
rpt_report_author,updatetime,DATETIME,True,True
rpt_report_capital,create_date,DATE,True,True
rpt_report_capital,report_year,INT(11),False,True
rpt_report_capital,entrytime,DATETIME,True,True
rpt_report_capital,updatetime,DATETIME,True,True
rpt_report_event,create_date,DATE,True,True
rpt_report_event,entrytime,DATETIME,True,True
rpt_report_event,updatetime,DATETIME,True,True
rpt_report_reliability,entrytime,DATETIME,True,True
rpt_report_reliability,updatetime,DATETIME,True,True
rpt_report_type,entrytime,DATETIME,True,True
rpt_report_type,updatetime,DATETIME,True,True
rpt_target_price_adjust,current_create_date,DATE,True,True
rpt_target_price_adjust,previous_create_date,DATE,True,True
rpt_target_price_adjust,entrytime,DATETIME,True,True
rpt_target_price_adjust,updatetime,DATETIME,True,True
rpt_word_segment_stk,create_date,DATE,True,True
rpt_word_segment_stk,entrytime,DATETIME,True,True
rpt_word_segment_stk,updatetime,DATETIME,True,True
rpt_word_segment_stk,gds_sync_time,DATETIME,True,True
rpt_word_segment_stk_1,create_date,DATE,True,True
rpt_word_segment_stk_1,entrytime,DATETIME,True,True
rpt_word_segment_stk_1,updatetime,DATETIME,True,True
rpt_word_segment_stk_1,gds_sync_time,DATETIME,True,True
rpt_word_segment_stk_2,create_date,DATE,True,True
rpt_word_segment_stk_2,entrytime,DATETIME,True,True
rpt_word_segment_stk_2,updatetime,DATETIME,True,True
rpt_word_segment_stk_2,gds_sync_time,DATETIME,True,True
rpt_word_segment_stk_3,create_date,DATE,True,True
rpt_word_segment_stk_3,entrytime,DATETIME,True,True
rpt_word_segment_stk_3,updatetime,DATETIME,True,True
rpt_word_segment_stk_3,gds_sync_time,DATETIME,True,True
