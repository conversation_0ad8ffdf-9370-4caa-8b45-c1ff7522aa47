{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "中国共同基金申购赎回情况[ChinaMutualFundPchRedm]", "ID": "ChinaMutualFundPchRedm", "HasAuth": true, "sortOrder": 0, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国ETF申购赎回[ETFPchRedm]", "ID": "ETFPchRedm", "HasAuth": true, "sortOrder": 1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国开放式基金场内申购赎回[LOFPchRedm]", "ID": "LOFPchRedm", "HasAuth": true, "sortOrder": 2, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国封闭式基金场内申购赎回[ClosedFundPchRedm]", "ID": "ClosedFundPchRedm", "HasAuth": true, "sortOrder": 3, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}], "PageInfo": null}, "success": true}