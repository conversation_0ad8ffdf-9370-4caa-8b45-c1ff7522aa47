﻿field_name,field_type
declare_date,DATETIME
chg_startdate,DATETIME
chg_enddate,DATETIME
xr_xd_date,DATETIME
entrytime,DATETIME
updatetime,<PERSON>ATETIME
found_date,<PERSON>ATETIME
publish_date,DATETIME
bench_date,<PERSON>ATETIME
change_period,VARCHAR(10)
end_date,DATETIME
distri_year,INT(11)
distridate_type,VARCHAR(10)
capbase_date,DATETIME
right_recorddate,<PERSON>AT<PERSON><PERSON><PERSON>
last_tradedate,DATETIME
cashdiv_startdate,DATETIME
cashdiv_enddate,DATETIME
stock_arrdate,<PERSON><PERSON><PERSON><PERSON><PERSON>
list_date,DATETIME
buyback_startdate,DATETIME
buyback_enddate,<PERSON><PERSON><PERSON><PERSON><PERSON>
ass_replacedate,<PERSON><PERSON><PERSON><PERSON><PERSON>
bdresol_declaredate,DATETIME
delist_date,DATETIME
con_date,DATE
con_year,INT(11)
con_or_hisdate,DATE
con_np_hisdate,<PERSON><PERSON><PERSON>
con_eps_hisdate,DATE
con_rating_hisdate,<PERSON>AT<PERSON>
con_target_price_hisdate,DATE
report_year,INT(11)
begin_date,DAT<PERSON>
end_date,DATE
REPORT_YEAR,INT(11)
ENTRYTIME,DATETIME
UPDATETIME,DATETIME
report_quarter,INT(11)
declare_date,DATE
appraisal_date,DATE
REPORT_QUARTER,VARCHAR(10)
DECLARE_DATE,DATETIME
BEGIN_DATE,DATETIME
END_DATE,DATETIME
REPORT_QUARTER,INT(11)
report_period,INT(11)
create_date,DATE
report_period,VARCHAR(20)
report_period_type,INT(11)
into_date,DATETIME
out_date,DATETIME
first_entrytime,DATETIME
out_declaredate,DATETIME
trade_date,DATETIME
into_date,DATE
out_date,DATE
PUBLISH_DATE,DATETIME
NTRADE_TIME,VARCHAR(10)
NTRADE_STARTDATE,DATETIME
NTRADE_ENDDATE,DATETIME
RTRADE_DATE,DATETIME
trade_date,DATE
BEGIN_DATE,DATE
END_DATE,DATE
ENTRY_TIME,DATETIME
UPDATE_TIME,DATETIME
TRADE_DATE,DATETIME
IS_TRADE_DATE,INT(11)
VTRADE_DATE,DATETIME
LTRADE_DATE,DATETIME
NTRADE_DATE,DATETIME
current_create_date,DATE
previous_create_date,DATE
settlement_date,VARCHAR(200)
gds_sync_time,DATETIME
