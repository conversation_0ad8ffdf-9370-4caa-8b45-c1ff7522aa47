# MySQL数据同步工具

这是一个功能完整的MySQL数据库同步工具，它可以将源数据库中的所有表结构和数据复制到目标数据库。

## 功能特点

- 完整同步数据库表结构和数据
- 多线程并行处理，提高同步效率
- 批量处理数据，减少内存占用
- 详细的日志记录和进度显示
- 支持两种配置方式：代码配置或命令行参数

## 依赖项

- Python 3.6+
- pymysql

## 安装依赖

```bash
pip install pymysql
```

## 使用方法

### 方法一：通过代码配置

1. 修改 `mysql_sync_tool.py` 文件中的数据库配置信息：

```python
# 源数据库配置
SOURCE_DB_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root", 
    "password": "password",
    "database": "source_db"
}

# 目标数据库配置
TARGET_DB_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "password",
    "database": "target_db"
}

# 并发线程数
MAX_WORKERS = 5
```

2. 直接运行脚本开始同步：

```bash
python mysql_sync_tool.py
```

### 方法二：通过命令行参数

使用命令行参数直接指定源数据库和目标数据库：

```bash
python mysql_sync_tool.py -s user:password@host:port/database -d user:password@host:port/database [-w 线程数]
```

参数说明：
- `-s, --source`：源数据库连接串，格式为 `user:password@host:port/database`
- `-d, --target`：目标数据库连接串，格式为 `user:password@host:port/database`
- `-w, --workers`：并行工作线程数，默认为代码中设置的值

示例：
```bash
python mysql_sync_tool.py -s root:password@localhost:3306/source_db -d root:password@remote_host:3306/target_db -w 8
```

### 混合使用

您也可以只指定部分参数，未指定的参数将使用代码中的默认值：

```bash
# 只指定源数据库，目标数据库使用代码中的配置
python mysql_sync_tool.py -s root:password@localhost:3306/source_db

# 只指定目标数据库，源数据库使用代码中的配置
python mysql_sync_tool.py -d root:password@remote_host:3306/target_db

# 只修改并发线程数
python mysql_sync_tool.py -w 10
```

## 工作原理

1. 连接源数据库和目标数据库
2. 获取源数据库中的所有表
3. 对于每个表：
   - 获取表结构并在目标数据库中创建
   - 批量读取表数据并写入目标数据库
4. 使用多线程并行处理不同表的同步工作
5. 输出同步统计信息

## 注意事项

- 该工具会先删除目标数据库中的同名表，再创建新表，请谨慎使用
- 同步过程中会生成日志文件，记录详细的同步进度和错误信息
- 建议在使用前备份目标数据库 