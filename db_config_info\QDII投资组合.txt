{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "QDII投资组合-地区配置[QDIIScopePortfolio]", "ID": "QDIIScopePortfolio", "HasAuth": true, "sortOrder": 0, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "QDII投资组合-行业配置[QDIIIndPortfolio]", "ID": "QDIIIndPortfolio", "HasAuth": true, "sortOrder": 1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "QDII投资组合-持券配置[QDIISecuritiesPortfolio]", "ID": "QDIISecuritiesPortfolio", "HasAuth": true, "sortOrder": 1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}], "PageInfo": null}, "success": true}