{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "中国共同基金净值[ChinaMutualFundNAV]", "ID": "ChinaMutualFundNAV", "HasAuth": true, "sortOrder": -1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国上市基金日行情[ChinaClosedFundEODPrice]", "ID": "ChinaClosedFundEODPrice", "HasAuth": true, "sortOrder": 0, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国货币式基金日收益[CMoneyMarketFIncome]", "ID": "CMoneyMarketFIncome", "HasAuth": true, "sortOrder": 1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国货币式基金日收益(拆分)[CMoneyMarketDailyFIncome]", "ID": "CMoneyMarketDailyFIncome", "HasAuth": true, "sortOrder": 2, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金停复牌[CMFTradingSuspension]", "ID": "CMFTradingSuspension", "HasAuth": true, "sortOrder": 3, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false}, {"DataType": 1, "Text": "中国共同基金份额[ChinaMutualFundShare]", "ID": "ChinaMutualFundShare", "HasAuth": true, "sortOrder": 4, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金场内流通份额[ChinaMutualFundFloatShare]", "ID": "ChinaMutualFundFloatShare", "HasAuth": true, "sortOrder": 5, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金净值操作记录表[CMFNAVOperationrecord]", "ID": "CMFNAVOperationrecord", "HasAuth": true, "sortOrder": 6, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金净值表现(报告期)[ChinaMutualFundRepNAVPer]", "ID": "ChinaMutualFundRepNAVPer", "HasAuth": true, "sortOrder": 7, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国货币式基金重要指标(报告期)[CMMQuarterlydata]", "ID": "CMMQuarterlydata", "HasAuth": true, "sortOrder": 8, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金业绩比较基准行情[ChinaMutualFundBenchmarkEOD]", "ID": "ChinaMutualFundBenchmarkEOD", "HasAuth": true, "sortOrder": 9, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金Wind基金仓位估算[ChinaMutualFundPosEstimation]", "ID": "ChinaMutualFundPosEstimation", "HasAuth": true, "sortOrder": 10, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金席位交易量及佣金[ChinaMutualFundSeatTrading]", "ID": "ChinaMutualFundSeatTrading", "HasAuth": true, "sortOrder": 11, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false}, {"DataType": 1, "Text": "中国共同基金利率敏感分析[Cfundratesensitive]", "ID": "Cfundratesensitive", "HasAuth": true, "sortOrder": 12, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国上市基金IOPV收盘净值[CMFIOPVNAV]", "ID": "CMFIOPVNAV", "HasAuth": true, "sortOrder": 13, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}], "PageInfo": null}, "success": true}