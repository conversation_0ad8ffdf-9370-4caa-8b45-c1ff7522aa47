# encoding: utf-8
import pandas as pd
import pymysql
import datetime
import sys
import json

new_path = ['D:\\0.维护代码\\1.数据库更新维护', ]
for path in new_path:
    if path not in sys.path:
        sys.path.append(path)


def wind_remote_fun():
    db_info = {
        'host': '**************',
        'port': 3306,
        'user': 'cusreader',
        'password': 'cus_reader@tf1603',
        'db': 'cus_fund_db2',
        'charset': 'utf8'
    }
    return pymysql.connect(**db_info)


def db_local_fun():
    db_info = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'db': 'cus_fund_db2',
        'charset': 'utf8'
    }
    return pymysql.connect(**db_info)


def convert_to_mysql_type(pymysql_type):
    map_dict = {
        pymysql.constants.FIELD_TYPE.VARCHAR: 'VARCHAR',
        pymysql.constants.FIELD_TYPE.CHAR: 'CHAR',
        pymysql.constants.FIELD_TYPE.TINY: 'TINYINT',
        pymysql.constants.FIELD_TYPE.LONG: 'INT',
        pymysql.constants.FIELD_TYPE.FLOAT: 'FLOAT',
        pymysql.constants.FIELD_TYPE.DOUBLE: 'DOUBLE',
        pymysql.constants.FIELD_TYPE.DECIMAL: 'DECIMAL',
        pymysql.constants.FIELD_TYPE.NEWDECIMAL: 'DECIMAL',
        pymysql.constants.FIELD_TYPE.LONGLONG: 'BIGINT',
        pymysql.constants.FIELD_TYPE.DATE: 'DATE',
        pymysql.constants.FIELD_TYPE.TIME: 'TIME',
        pymysql.constants.FIELD_TYPE.DATETIME: 'DATETIME',
        pymysql.constants.FIELD_TYPE.TIMESTAMP: 'TIMESTAMP',
        pymysql.constants.FIELD_TYPE.LONG_BLOB: 'LONGBLOB',
        pymysql.constants.FIELD_TYPE.MEDIUM_BLOB: 'MEDIUMBLOB',
        pymysql.constants.FIELD_TYPE.BLOB: 'LONGTEXT',
        pymysql.constants.FIELD_TYPE.TINY_BLOB: 'TINYBLOB',
        pymysql.constants.FIELD_TYPE.STRING: 'CHAR',
        pymysql.constants.FIELD_TYPE.JSON: 'JSON'
    }
    return map_dict.get(pymysql_type, 'VARCHAR')


def get_all_table_names(connector):
    cur = connector.cursor()
    cur.execute("SHOW TABLES")
    results = cur.fetchall()
    tables = [result[0] for result in results]
    return tables


def get_remote_table_list():
    """从远程数据库直接获取表名列表"""
    cursor = wind_remote_fun().cursor()
    cursor.execute("SHOW TABLES")
    tables = [row[0] for row in cursor.fetchall()]
    return tables


def creat_new_table_frame(table_name):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()
    cursor_remote.execute(f"SELECT * FROM {table_name} LIMIT 0")
    desc = cursor_remote.description
    sql_create_table = ""
    primary_keys = []
    
    for column in desc:
        field_name = column[0]
        field_type = column[1]
        internal_size = column[3]
        scale = column[5]
        null_ok = column[6]
        type = convert_to_mysql_type(field_type)
        if type in ["VARCHAR"]:
            type = type + f"({internal_size})"
        if type in ["DECIMAL"]:
            type = type + f"({internal_size},{scale})"
        if null_ok == False:
            primary_keys.append(field_name)
        sql_create_table += f"`{field_name}`" + " " + type + ", "
    
    # 如果有主键，添加主键约束
    if primary_keys:
        primary_key_str = ", ".join([f"`{pk}`" for pk in primary_keys])
        sql_create_table += f"PRIMARY KEY ({primary_key_str})"
    else:
        # 移除最后的逗号和空格
        sql_create_table = sql_create_table[:-2]
    
    sql_create_table = f"CREATE TABLE {table_name} ({sql_create_table})"
    # 创建新表
    cursor_local.execute(sql_create_table)
    db_local.commit()


def get_table_size(table_name):
    cursor_remote = wind_remote_fun().cursor()
    sql = f"SELECT round(((data_length + index_length) / 1024 / 1024), 2) `Size in MB` FROM information_schema.TABLES WHERE table_schema = 'wind' AND table_name = '{table_name}'"
    cursor_remote.execute(sql)
    result = cursor_remote.fetchone()
    return result[0]


def get_table_count(connect, table_name):
    cursor = connect.cursor()
    sql = f"SELECT COUNT(*) FROM {table_name}"
    cursor.execute(sql)
    result = cursor.fetchone()
    return result[0]


def get_table_entry_date_range(connection, table_name):
    with connection.cursor() as cur:
        sql = f"SELECT MIN(entrytime), MAX(entrytime) FROM {table_name}"
        cur.execute(sql)
        result = cur.fetchone()
        return result[0], result[1]


def _insert_data(cursor, table_name, data, columns):
    try:
        column_sql = f'{",".join(columns)}'
        placeholders = ', '.join(['%s'] * len(columns))
        query = f"INSERT INTO {table_name} ({column_sql}) VALUES ({placeholders})"
        cursor.executemany(query, data)
    except Exception as e:
        print(f"Error occurred: {e}. Switching to temporary table method...")
        cursor.execute("DROP TEMPORARY TABLE IF EXISTS temp_table")
        cursor.execute(f"CREATE TEMPORARY TABLE temp_table LIKE {table_name}")
        query = f"INSERT INTO temp_table ({column_sql}) VALUES ({placeholders})"
        cursor.executemany(query, data)
        cursor.execute(f"DELETE main FROM {table_name} main JOIN temp_table temp ON main.ID = temp.ID")
        cursor.execute(f"INSERT INTO {table_name} SELECT * FROM temp_table")


def _delete_by_id(cursor, table_name, ids, batch_size=10000):
    for start_idx in range(0, len(ids), batch_size):
        batch_ids = ids[start_idx:start_idx + batch_size]
        placeholders = ', '.join(['%s'] * len(batch_ids))
        sql_delete = f"DELETE FROM {table_name} WHERE ID IN ({placeholders})"
        cursor.execute(sql_delete, batch_ids)
        print(f"已删除 {len(batch_ids)} 行从 {table_name}")
        print(f"删除的 ID: {batch_ids}")


def write_new_table_all_data(table_name):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()
    sql_fetch = f"SELECT * FROM {table_name}"
    cursor_remote.execute(sql_fetch)
    data = cursor_remote.fetchall()
    desc = cursor_remote.description
    columns = [column[0] for column in desc]

    _insert_data(cursor_local, table_name, data, columns)
    db_local.commit()


def write_new_table_separately(table_name, N=2):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()

    # 获取entrytime的范围
    min_entrytime, max_entrytime = get_table_entry_date_range(wind_remote_fun(), table_name)
    min_entrytime = min_entrytime.date()  
    max_entrytime = (max_entrytime + datetime.timedelta(days=1)).date()
    if max_entrytime < pd.Timestamp('2018-01-01').date():
        print(f"数据异常，{table_name}的最大日期为{max_entrytime}")
        return 'old'
    current_entrytime = min_entrytime

    desc = None
    columns = None
    while current_entrytime < max_entrytime:
        next_entrytime = current_entrytime + datetime.timedelta(days=N)

        # 根据日期范围查询数据
        sql_fetch = f"SELECT * FROM {table_name} WHERE entrytime >= %s AND entrytime < %s"
        cursor_remote.execute(sql_fetch, (current_entrytime, next_entrytime))
        data = cursor_remote.fetchall()

        if not data:  # 如果没有数据，跳过这个日期范围
            current_entrytime = next_entrytime
            continue

        if desc is None:
            desc = cursor_remote.description
            columns = [column[0] for column in desc]

        _insert_data(cursor_local, table_name, data, columns)
        db_local.commit()

        print(f"已写入{current_entrytime}到{next_entrytime}的数据: {len(data)}条")
        current_entrytime = next_entrytime


def update_table(table_name, N=2):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()

    # 获取entrytime的范围
    min_entrytime_local, max_entrytime_local = get_table_entry_date_range(db_local, table_name)
    min_entrytime_remote, max_entrytime_remote = get_table_entry_date_range(wind_remote_fun(), table_name)
    if max_entrytime_remote < pd.Timestamp('2018-01-01'):
        print(f"数据异常，{table_name}的最大日期为{max_entrytime_remote}")
        return 'old'

    current_entrytime = max_entrytime_local

    desc = None
    columns = None
    while current_entrytime < max_entrytime_remote:
        next_entrytime = current_entrytime + datetime.timedelta(days=N)

        # 根据日期范围查询数据
        sql_fetch = f"SELECT * FROM {table_name} WHERE entrytime > %s AND entrytime < %s"
        cursor_remote.execute(sql_fetch, (current_entrytime, next_entrytime))
        data = cursor_remote.fetchall()

        if not data:  # 如果没有数据，跳过这个日期范围
            current_entrytime = next_entrytime
            continue

        if desc is None:
            desc = cursor_remote.description
            columns = [column[0] for column in desc]

        _insert_data(cursor_local, table_name, data, columns)
        db_local.commit()

        print(f"已写入{current_entrytime}到{next_entrytime}的数据: {len(data)}条")
        current_entrytime = next_entrytime


def compare_id(table_name):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()

    # 获取远程和本地的所有ID
    cursor_remote.execute(f"SELECT ID FROM {table_name}")
    ids_remote = set([row[0] for row in cursor_remote.fetchall()])

    cursor_local.execute(f"SELECT ID FROM {table_name}")
    ids_local = set([row[0] for row in cursor_local.fetchall()])

    # 获取本地有但是远程没有的ID
    unused_ids = list(ids_local - ids_remote)
    print(f"本地有但是远程没有的ID数量: {len(unused_ids)}")

    # 获取远程有但是本地没有的ID
    missing_ids = list(ids_remote - ids_local)
    print(f"远程有但是本地没有的ID数量: {len(missing_ids)}")

    if len(unused_ids) == 0 and len(missing_ids) == 0:
        print(f"本地和远程的ID完全一致！")
        return True, unused_ids, missing_ids
    else:
        print(f"本地和远程的ID不一致！")
        return False, unused_ids, missing_ids


def update_by_id(table_name, batch_size=10000):
    cursor_remote = wind_remote_fun().cursor()
    db_local = db_local_fun()
    cursor_local = db_local.cursor()

    # 比较ID并获取差异
    identical, unused_ids, missing_ids = compare_id(table_name)
    if identical:
        print(f"ID全部一致！{table_name}已经是最新的了！")
        return

    # 删除本地多余的ID
    if unused_ids:
        _delete_by_id(cursor_local, table_name, unused_ids)
        db_local.commit()

    # 添加缺失的ID对应的数据
    if missing_ids:
        print(f"缺失的ID数量: {len(missing_ids)}")
        desc = None
        columns = None

        for start_idx in range(0, len(missing_ids), batch_size):
            batch_ids = missing_ids[start_idx:start_idx + batch_size]

            placeholders = ', '.join(['%s'] * len(batch_ids))
            sql_fetch = f"SELECT * FROM {table_name} WHERE ID IN ({placeholders})"
            cursor_remote.execute(sql_fetch, batch_ids)
            data = cursor_remote.fetchall()

            if not data:
                continue

            if desc is None:
                desc = cursor_remote.description
                columns = [column[0] for column in desc]

            _insert_data(cursor_local, table_name, data, columns)
            db_local.commit()

            print(f"已写入缺失的ID数据: {len(data)}条")


def update_table_main(table_to_remove=None, table_start=None):
    """主更新函数，直接从远程数据库获取表名列表"""
    remote_table_list = get_remote_table_list()
    if table_start:
        remote_table_list = remote_table_list[table_start:]
    if table_to_remove:
        try:
            remote_table_list.remove(table_to_remove)
        except:
            print(f"表{table_to_remove}不存在！")

    local_table_list = get_all_table_names(db_local_fun())

    for table_name in remote_table_list:
        print(f"开始更新{table_name}...")
        try:
            if table_name.lower() not in local_table_list:
                print(f"本地没有{table_name}，开始创建...")
                creat_new_table_frame(table_name)
                print(f"创建{table_name}成功！")
                print(f"开始写入{table_name}...")
                num_remote = get_table_count(wind_remote_fun(), table_name)
                if num_remote < 100000:
                    write_new_table_all_data(table_name)
                else:
                    print(f"分批写入{table_name}")
                    lb = write_new_table_separately(table_name)
                    if lb == 'old':
                        print(f"{table_name}数据太老，不获取！")
                        continue
                print(f"写入{table_name}成功！")
                
                # 验证数据完整性
                num_local = get_table_count(db_local_fun(), table_name)
                print(f"远程数量：{num_remote}")
                print(f"本地数量：{num_local}")
                if num_remote != num_local:
                    print(f"{table_name}数量不相等！")
                    print(f"利用ID更新{table_name}")
                    update_by_id(table_name)
                    num_local = get_table_count(db_local_fun(), table_name)
                    if num_remote == num_local:
                        print(f"更新{table_name}成功！")
                    else:
                        print(f"数量依旧不相等,{table_name}更新失败！")
                else:
                    print(f"更新{table_name}成功！")
            else:
                print(f"本地已有{table_name}，开始更新...")
                update_table(table_name)
                
                # 验证数据完整性
                num_remote = get_table_count(wind_remote_fun(), table_name)
                num_local = get_table_count(db_local_fun(), table_name)
                print(f"远程数量：{num_remote}")
                print(f"本地数量：{num_local}")
                if num_remote != num_local:
                    print(f"{table_name}数量不相等！")
                    print(f"利用ID更新{table_name}")
                    update_by_id(table_name)
                    num_local = get_table_count(db_local_fun(), table_name)
                    if num_remote == num_local:
                        identical_id = compare_id(table_name)[0]
                        if identical_id:
                            print(f"ID完全一致！更新{table_name}成功！")
                        else:
                            print(f"ID不一致！{table_name}更新失败！")
                    else:
                        print(f"数量依旧不相等,{table_name}更新失败！")
                else:
                    identical_id = compare_id(table_name)[0]
                    if identical_id:
                        print(f"ID完全一致！更新{table_name}成功！")
                    else:
                        print(f"ID不一致！{table_name}更新失败！")

        except Exception as e:
            print(f"{table_name}更新失败！")
            print(e)

        print(f"{table_name}更新完毕！")
        print("---------------------------------------------------")




if __name__ == '__main__':
    table_to_remove = []
    table_start = 0
    update_table_main(*table_to_remove, table_start=table_start)