{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "中国共同基金基本资料[ChinaMutualFundDescription]", "ID": "ChinaMutualFundDescription", "HasAuth": true, "sortOrder": -19, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false}, {"DataType": 1, "Text": "中国保本基金基本资料[CPFundDescription]", "ID": "CPFundDescription", "HasAuth": true, "sortOrder": -18, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国LOF基金基本资料[LOFDescription]", "ID": "LOFDescription", "HasAuth": true, "sortOrder": -16, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国分级基金基本资料[ChinaGradingFund]", "ID": "ChinaGradingFund", "HasAuth": true, "sortOrder": -15, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国联接基金基本资料[ChinaFeederFund]", "ID": "ChinaFeederFund", "HasAuth": true, "sortOrder": -14, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金基金经理[ChinaMutualFundManager]", "ID": "ChinaMutualFundManager", "HasAuth": true, "sortOrder": -13, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国开放式基金费率表[CMFSubredFee]", "ID": "CMFSubredFee", "HasAuth": true, "sortOrder": -12, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金代销机构[CMFSellingAgents]", "ID": "CMFSellingAgents", "HasAuth": true, "sortOrder": -11, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false}, {"DataType": 1, "Text": "中国共同基金暂停申购赎回[ChinaMutualFundSuspendPchRedm]", "ID": "ChinaMutualFundSuspendPchRedm", "HasAuth": true, "sortOrder": -10, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金初始风险等级[CMFRiskLevel]", "ID": "CMFRiskLevel", "HasAuth": true, "sortOrder": -9, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金业绩比较基准配置[ChinaMutualFundBenchMark]", "ID": "ChinaMutualFundBenchMark", "HasAuth": true, "sortOrder": -8, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金申购赎回天数[CFundPchRedm]", "ID": "CFundPchRedm", "HasAuth": true, "sortOrder": -7, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金滚动运作周期[CMFundOperatePeriod]", "ID": "CMFundOperatePeriod", "HasAuth": true, "sortOrder": -6, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国货币市场基金份额结转方式[CMoneyMarketFSCarryOverm]", "ID": "CMoneyMarketFSCarryOverm", "HasAuth": true, "sortOrder": -5, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金被动型基金跟踪指数[ChinaMutualFundTrackingIndex]", "ID": "ChinaMutualFundTrackingIndex", "HasAuth": true, "sortOrder": -4, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金曾用名[CFundPreviousName]", "ID": "CFundPreviousName", "HasAuth": true, "sortOrder": -3, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金业务代码及简称[CMFCodeAndSName]", "ID": "CMFCodeAndSName", "HasAuth": true, "sortOrder": -2, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国Wind基金分类[ChinaMutualFundSector]", "ID": "ChinaMutualFundSector", "HasAuth": true, "sortOrder": -1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金Wind概念分类[CMFConseption]", "ID": "CMFConseption", "HasAuth": true, "sortOrder": 0, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金WIND行业板块主题分类[CMFIndustryplate]", "ID": "CMFIndustryplate", "HasAuth": true, "sortOrder": 4, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金WIND概念板块主题分类[CMFThemeConcept]", "ID": "CMFThemeConcept", "HasAuth": true, "sortOrder": 5, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金公司派系风格[CFundFactionalStyle]", "ID": "CFundFactionalStyle", "HasAuth": true, "sortOrder": 6, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金股票风格分类门限值[CFundStyleThreshold]", "ID": "CFundStyleThreshold", "HasAuth": true, "sortOrder": 7, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金风格系数[CFundStyleCoefficient]", "ID": "CFundStyleCoefficient", "HasAuth": true, "sortOrder": 8, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金金融机构资格[FinancialQualification]", "ID": "FinancialQualification", "HasAuth": true, "sortOrder": 18, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金公司曾用名[CFundCompanyPreviousName]", "ID": "CFundCompanyPreviousName", "HasAuth": true, "sortOrder": 19, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金优惠费率（废弃）[CMFPreferentialFee]", "ID": "CMFPreferentialFee", "HasAuth": true, "sortOrder": 20, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金投资品种比例信息[CMFProportionOfInveObj]", "ID": "CMFProportionOfInveObj", "HasAuth": true, "sortOrder": 21, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false}, {"DataType": 1, "Text": "中国共同基金中介机构[ChinaMutualFundAgency]", "ID": "ChinaMutualFundAgency", "HasAuth": true, "sortOrder": 22, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false}, {"DataType": 1, "Text": "中国共同基金基本资料属性变更[CMFDESCChange]", "ID": "CMFDESCChange", "HasAuth": true, "sortOrder": 23, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金证监会分类[CMFSECClass]", "ID": "CMFSECClass", "HasAuth": true, "sortOrder": 24, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国ETF基金投资范围分类[ChinaETFInvestClass]", "ID": "ChinaETFInvestClass", "HasAuth": true, "sortOrder": 25, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金基金经理资历表[CMFMGQualifications]", "ID": "CMFMGQualifications", "HasAuth": true, "sortOrder": 26, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}], "PageInfo": null}, "success": true}