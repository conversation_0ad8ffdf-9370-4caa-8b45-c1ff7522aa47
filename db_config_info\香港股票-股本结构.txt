{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "香港股票发行数量[HKShareCapitalization]", "ID": "HKShareCapitalization", "HasAuth": true, "sortOrder": 0, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "香港股票股本结构[HKEquityStruc]", "ID": "HKEquityStruc", "HasAuth": true, "sortOrder": 1, "IsContract": true, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false, "IsRoot": false}, {"DataType": 1, "Text": "香港股票自由流通股本[HKShareFreeFloat]", "ID": "HKShareFreeFloat", "HasAuth": true, "sortOrder": 2, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "香港股票存托凭证份额[HDRInfo]", "ID": "HDRInfo", "HasAuth": true, "sortOrder": 3, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "香港证券回购信息[HKStockRepo]", "ID": "HKStockRepo", "HasAuth": true, "sortOrder": 3, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}], "PageInfo": null}, "success": true}