# encoding: utf-8
import schedule
import time
import pandas as pd
import os

from wind_database_maintain_dm import update_table_main, read_table_info, get_all_table_names, wind_remote_fun

def read_tables_from_file(file_path):
    """从文件中读取表名"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            tables = [line.strip() for line in f]
        return tables
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
        return []

def read_table_config_csv(csv_path='table_config.csv'):
    """从CSV配置文件中读取表配置"""
    try:
        df = pd.read_csv(csv_path, encoding='utf-8-sig')
        print(f"成功读取CSV配置文件: {csv_path}")
        print(f"配置文件包含 {len(df)} 个表")
        
        # 获取需要更新的表（是否更新=1）
        tables_to_update = df[df['是否更新'] == 1]['表名'].tolist()
        
        # 获取需要检查的表（数目不相等时是否检查=1）
        tables_to_check = df[df['数目不相等时是否检查'] == 1]['表名'].tolist()
        
        print(f"需要更新的表数量: {len(tables_to_update)}")
        print(f"需要检查的表数量: {len(tables_to_check)}")
        
        return {
            'all_tables': df,
            'tables_to_update': tables_to_update,
            'tables_to_check': tables_to_check
        }
    except Exception as e:
        print(f"读取CSV配置文件时出错: {str(e)}")
        return None

def update_daily_fun():
    remote_table_list = ['AShareEODDerivativeIndicator', 'AShareEODPrices', 'AShareFinancialIndicator', 'AShareTTMHis',
                         'AShareIncome', 'AShareDescription', 'AShareProfitExpress', 'AShareProfitNotice',
                         'AShareYield']
    print(f'{pd.to_datetime("today")} - Start updating daily tables: {remote_table_list}')
    update_table_main(remote_table_list, check_id=False)
    print(f'{pd.to_datetime("today")} - Finished updating daily tables: {remote_table_list}')

def main(csv_path='table_config.csv'):
    """根据CSV配置文件更新表"""
    config = read_table_config_csv(csv_path)
    if not config:
        print("无法读取CSV配置文件，退出更新")
        return
    
    tables_to_update = config['tables_to_update']
    tables_to_check = config['tables_to_check']
    
    if not tables_to_update:
        print("没有需要更新的表")
        return
    
    print(f'{pd.to_datetime("today")} - 开始根据CSV配置更新表')
    print(f'需要更新的表: {tables_to_update}')
    
    for i, table in enumerate(tables_to_update):
        print('**********************************************************************************')
        print(f'{i+1}/{len(tables_to_update)} - 开始更新表: {table}')
        
        # 判断是否需要检查OBJECT_ID
        check_id = table in tables_to_check
        if check_id:
            print(f"{table} 将进行OBJECT_ID检查")
        
        try:
            update_table_main([table], check_id=check_id)
            print(f'{pd.to_datetime("today")} - 成功更新表: {table}')
        except Exception as e:
            print(f'{pd.to_datetime("today")} - 更新表 {table} 时出错: {str(e)}')
        
        print('**********************************************************************************')
    
    print(f'{pd.to_datetime("today")} - 根据CSV配置更新表完成')

def get_table_files():
    file_dir = 'db_config_info'
    txt_files = [f[:-4] for f in os.listdir(file_dir) if f.endswith('.txt')]
    print(f'Found {len(txt_files)} table config files: {txt_files}')
    return txt_files

def update_fun(table_to_remove=None,start=None,end=None,check_id=False):

    # txts = get_table_files()
    # remote_table_list = \
    #     read_table_info(*txts)['ID'].tolist()
    # print(f'{len(remote_table_list)} tables found')
    remote_table_list = get_all_table_names(wind_remote_fun())

    if table_to_remove:
        table_to_remove = [table.lower() for table in table_to_remove]


    for i in range(len(remote_table_list)):
        print('**********************************************************************************')
        print(f'{i} - {remote_table_list[i]}')
        table = remote_table_list[i]
        print(f'{i}')

        if table.lower() in table_to_remove:
            print(f'{table} will be removed')
            continue
        if i < start:
            print(f'{table} will be skipped')
            continue
        elif i > end:
            print(f'{table} will be skipped')
            continue
        else:
            print(f'{table} will be updated')

        print(f'{pd.to_datetime("today")} - Start updating tables: {table}')
        update_table_main([table], check_id=check_id)
        print(f'{pd.to_datetime("today")} - Finished updating tables: {table}')
        print('**********************************************************************************')



if __name__ == '__main__':
    # 选择更新模式
    use_csv_config = True  # 设置为True使用CSV配置，False使用原有逻辑
    
    if use_csv_config:
        # 使用CSV配置文件进行更新
        print("使用CSV配置文件进行表更新...")
        main('table_config.csv')
    else:
        # 原有的更新逻辑
        print("使用原有逻辑进行表更新...")
        # 从文件中读取要删除的表
        discarded_tables = read_tables_from_file("discarded_tables.txt")
        large_tables = read_tables_from_file("large_tables copy.txt")
        windhash_tables = read_tables_from_file("windhash_tables.txt")
        
        # 合并所有要删除的表
        table_to_remove = discarded_tables + windhash_tables + large_tables
        table_to_remove = [table.split('\t')[0] if '\t' in table else table for table in table_to_remove]
        table_to_remove = table_to_remove + ['shscmechanismownership']

        start = 750
        end = 1000
        update_fun(table_to_remove,start,end,check_id=False)
    
    print('**********************************************************************************')
