# encoding: utf-8
import schedule
import time
import pandas as pd
import os

from wind_database_maintain import update_table_main, read_table_info, get_all_table_names, wind_remote_fun

def read_tables_from_file(file_path):
    """从文件中读取表名"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            tables = [line.strip() for line in f]
        return tables
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
        return []

def update_daily_fun():
    remote_table_list = ['AShareEODDerivativeIndicator', 'AShareEODPrices', 'AShareFinancialIndicator', 'AShareTTMHis',
                         'AShareIncome', 'AShareDescription', 'AShareProfitExpress', 'AShareProfitNotice',
                         'AShareYield']
    print(f'{pd.to_datetime("today")} - Start updating daily tables: {remote_table_list}')
    update_table_main(remote_table_list, check_id=False)
    print(f'{pd.to_datetime("today")} - Finished updating daily tables: {remote_table_list}')

def get_table_files():
    file_dir = 'db_config_info'
    txt_files = [f[:-4] for f in os.listdir(file_dir) if f.endswith('.txt')]
    print(f'Found {len(txt_files)} table config files: {txt_files}')
    return txt_files

def update_fun(table_to_remove=None,start=None,end=None,check_id=False):

    # txts = get_table_files()
    # remote_table_list = \
    #     read_table_info(*txts)['ID'].tolist()
    # print(f'{len(remote_table_list)} tables found')
    remote_table_list = get_all_table_names(wind_remote_fun())

    if table_to_remove:
        table_to_remove = [table.lower() for table in table_to_remove]


    for i in range(len(remote_table_list)):
        print('**********************************************************************************')
        print(f'{i} - {remote_table_list[i]}')
        table = remote_table_list[i]
        print(f'{i}')

        if table.lower() in table_to_remove:
            print(f'{table} will be removed')
            continue
        if i < start:
            print(f'{table} will be skipped')
            continue
        elif i > end:
            print(f'{table} will be skipped')
            continue
        else:
            print(f'{table} will be updated')

        print(f'{pd.to_datetime("today")} - Start updating tables: {table}')
        update_table_main([table], check_id=check_id)
        print(f'{pd.to_datetime("today")} - Finished updating tables: {table}')
        print('**********************************************************************************')



if __name__ == '__main__':
    # 从文件中读取要删除的表
    discarded_tables = read_tables_from_file("discarded_tables.txt")
    large_tables = read_tables_from_file("large_tables copy.txt")
    windhash_tables = read_tables_from_file("windhash_tables.txt")
    
    # 合并所有要删除的表
    table_to_remove = discarded_tables + windhash_tables + large_tables
    table_to_remove = [table.split('\t')[0] if '\t' in table else table for table in table_to_remove]
    table_to_remove = table_to_remove + ['shscmechanismownership']

    start = 750
    end = 1000
    update_fun(table_to_remove,start,end,check_id=False)
    print('**********************************************************************************')
