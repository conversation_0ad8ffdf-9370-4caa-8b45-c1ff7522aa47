{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "香港股票恒生行业分类[HKStockHSIndustriesMembers]", "ID": "HKStockHSIndustriesMembers", "HasAuth": true, "sortOrder": 0, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#C5C5C5", "AuthType": 0, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "香港股票GICS行业成份(废弃)[HKStockGICSIndustriesMembers]", "ID": "HKStockGICSIndustriesMembers", "HasAuth": true, "sortOrder": 1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#C5C5C5", "AuthType": 0, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "港股通申万行业分类（一年更新一次）[HKShareSWIndustriesClass]", "ID": "HKShareSWIndustriesClass", "HasAuth": true, "sortOrder": 2, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#C5C5C5", "AuthType": 0, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "港股通申万行业分类(2021版)[HKShareSWNIndustriesClass]", "ID": "HKShareSWNIndustriesClass", "HasAuth": true, "sortOrder": 3, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#C5C5C5", "AuthType": 0, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "中国外汇交易行情[FXEODPrices]", "ID": "FXEODPrices", "HasAuth": true, "sortOrder": 4, "IsContract": true, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}, {"DataType": 1, "Text": "港股通中信行业分类[HKSharesCITICSIndustriesClass]", "ID": "HKSharesCITICSIndustriesClass", "HasAuth": true, "sortOrder": 5, "IsContract": true, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "hasChildProduct": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true, "IsRoot": false}], "PageInfo": null}, "success": true}