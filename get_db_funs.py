# encoding: utf-8
import pandas as pd
from typing import Optional, Dict, List, Union, Any

DB_TYPE = 'DM'

def db_oracle_connect():
    import cx_Oracle
    host = "***********"  # wind数据库114
    port = "1521"  # 端口
    sid = "wind"  # 数据库名称
    username = 'wind'  # 用这个只读账号的SQL必须加表空间的名字,即WINDDATA
    password = 'wind'
    dsn = cx_Oracle.makedsn(host, port, sid)
    return cx_Oracle.connect(username, password, dsn)

def db_dm_connect():
    import dmPython
    dm_db_config = {
        'user': 'WD_READ_ONLY',
        'password': 'Wdzdschj@20250613',
        'server': '************',
        'port': 5236,
        'autoCommit': True
    }
    return dmPython.connect(**dm_db_config)

def db_mysql_connect(db='wind'):
    import pymysql
    db_info = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'db': db,
        'charset': 'utf8'
    }
    return pymysql.connect(**db_info)

def db_mysql_cus_connect(db='cus_fund_db2'):
    import pymysql
    db_info = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'db': db,
        'charset': 'utf8'
    }
    return pymysql.connect(**db_info)

def _build_condition_mysql(field: str, value: Any, params: List) -> str:
    if value is None:
        return None
    
    if isinstance(value, tuple) and len(value) == 2:
        operator, val = value
        if operator in [">", "<", ">=", "<=", "=", "!=", "LIKE", "REGEXP"]:
            params.append(val)
            return f"`{field}` {operator} %s"
        elif operator in ["IS NULL", "IS NOT NULL"]:
            return f"`{field}` {operator}"
        else:
            raise ValueError(f"Invalid operator: {operator}")
    elif isinstance(value, list):
        if len(value) == 1:
            params.append(value[0])
            return f"`{field}` = %s"
        elif len(value) == 2:
            params.extend(value)
            return f"`{field}` BETWEEN %s AND %s"
        else:
            placeholders = ', '.join(['%s'] * len(value))
            params.extend(value)
            return f"`{field}` IN ({placeholders})"
    else:
        params.append(value)
        return f"`{field}` = %s"

def _build_condition_dm(field: str, value: Any, params: List) -> str:
    if value is None:
        return None
    
    if isinstance(value, tuple) and len(value) == 2:
        operator, val = value
        if operator in [">", "<", ">=", "<=", "=", "!=", "LIKE"]:
            params.append(val)
            return f'"{field}" {operator} ?'
        elif operator == "REGEXP":
            params.append(val)
            return f'REGEXP_LIKE("{field}", ?)'
        elif operator in ["IS NULL", "IS NOT NULL"]:
            return f'"{field}" {operator}'
        else:
            raise ValueError(f"Invalid operator: {operator}")
    elif isinstance(value, list):
        if len(value) == 1:
            params.append(value[0])
            return f'"{field}" = ?'
        elif len(value) == 2:
            params.extend(value)
            return f'"{field}" BETWEEN ? AND ?'
        else:
            placeholders = ', '.join(['?'] * len(value))
            params.extend(value)
            return f'"{field}" IN ({placeholders})'
    else:
        params.append(value)
        return f'"{field}" = ?'

def _build_condition_oracle(field: str, value: Any, params: List) -> str:
    if value is None:
        return None
    
    if isinstance(value, tuple) and len(value) == 2:
        operator, val = value
        if operator in [">", "<", ">=", "<=", "=", "!=", "LIKE"]:
            params.append(val)
            return f'"{field}" {operator} :{len(params)}'
        elif operator == "REGEXP":
            params.append(val)
            return f'REGEXP_LIKE("{field}", :{len(params)})'
        elif operator in ["IS NULL", "IS NOT NULL"]:
            return f'"{field}" {operator}'
        else:
            raise ValueError(f"Invalid operator: {operator}")
    elif isinstance(value, list):
        if len(value) == 1:
            params.append(value[0])
            return f'"{field}" = :{len(params)}'
        elif len(value) == 2:
            params.extend(value)
            return f'"{field}" BETWEEN :{len(params)-1} AND :{len(params)}'
        else:
            placeholders = ', '.join([f':{len(params) + i + 1}' for i in range(len(value))])
            params.extend(value)
            return f'"{field}" IN ({placeholders})'
    else:
        params.append(value)
        return f'"{field}" = :{len(params)}'

def _build_complex_conditions(conditions: Dict[str, Any], params: List, db_type: str) -> str:
    if not conditions:
        return ""
    
    # 选择对应的条件构建函数
    if db_type == 'MYSQL':
        build_condition = _build_condition_mysql
    elif db_type == 'DM':
        build_condition = _build_condition_dm
    elif db_type == 'ORACLE':
        build_condition = _build_condition_oracle
    else:
        raise ValueError(f"不支持的数据库类型: {db_type}")
    
    condition_parts = []
    
    for key, value in conditions.items():
        if key == 'AND':
            if isinstance(value, list) and len(value) > 0:
                and_conditions = []
                for sub_condition in value:
                    if isinstance(sub_condition, dict):
                        sub_sql = _build_complex_conditions(sub_condition, params, db_type)
                        if sub_sql:
                            and_conditions.append(sub_sql)
                if and_conditions:
                    if len(and_conditions) == 1:
                        condition_parts.append(and_conditions[0])
                    else:
                        condition_parts.append(f"({' AND '.join(and_conditions)})")
        elif key == 'OR':
            if isinstance(value, list) and len(value) > 0:
                or_conditions = []
                for sub_condition in value:
                    if isinstance(sub_condition, dict):
                        sub_sql = _build_complex_conditions(sub_condition, params, db_type)
                        if sub_sql:
                            or_conditions.append(sub_sql)
                if or_conditions:
                    if len(or_conditions) == 1:
                        condition_parts.append(or_conditions[0])
                    else:
                        condition_parts.append(f"({' OR '.join(or_conditions)})")
        else:
            # 普通字段条件
            condition_sql = build_condition(key, value, params)
            if condition_sql:
                condition_parts.append(condition_sql)
    
    if len(condition_parts) == 1:
        return condition_parts[0]
    elif len(condition_parts) > 1:
        return f"({' AND '.join(condition_parts)})"
    else:
        return ""

def get_mysql_db_data(table_name, conn_fun=db_mysql_connect, keywords=None, additional_conditions=None):
    import pymysql
    conn = None
    cursor = None
    
    try:
        conn = conn_fun()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        if keywords is None:
            keys = '*'
        else:
            keys = ','.join(['`' + key + '`' for key in keywords])
        sql = f"SELECT {keys} FROM `{table_name}`"

        params = []
        
        if additional_conditions:
            where_clause = _build_complex_conditions(additional_conditions, params, 'MYSQL')
            if where_clause:
                sql += f' WHERE {where_clause}'

        cursor.execute(sql, tuple(params))
        results = cursor.fetchall()
        df = pd.DataFrame(results)
        
        return df
        
    except Exception as e:
        print(e)
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_dm_data(table_name, conn_fun=db_dm_connect, keywords=None, additional_conditions=None):
    conn = None
    cursor = None

    table_name = table_name.upper()
    if not table_name.startswith('WINDZX'):
        table_name = 'WINDZX.' + table_name
    
    try:
        conn = conn_fun()
        cursor = conn.cursor()

        if keywords is None:
            keys = '*'
        else:
            keys = ','.join([f'"{key}"' for key in keywords])  # 达梦使用双引号
        
        sql = f"SELECT {keys} FROM {table_name}"  # 达梦不需要反引号包围表名

        params = []
        
        if additional_conditions:
            where_clause = _build_complex_conditions(additional_conditions, params, 'DM')
            if where_clause:
                sql += f' WHERE {where_clause}'

        cursor.execute(sql, tuple(params))
        results = cursor.fetchall()
        
        # 获取列名
        column_names = [desc[0] for desc in cursor.description]
        
        # 创建DataFrame
        df = pd.DataFrame(results, columns=column_names)
        
        return df
        
    except Exception as e:
        print(e)
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_oracle_db_data(table_name, conn_fun=db_oracle_connect, keywords=None, additional_conditions=None):
    def split_fun(conditions):
        split_conditions = [[]]
        for field, value in conditions.items():
            if isinstance(value, list) and len(value) > 1000:
                chunks = [value[i:i + 1000] for i in range(0, len(value), 1000)]
                split_conditions = [current + [(field, chunk)] for current in split_conditions for chunk in chunks]
            else:
                split_conditions = [current + [(field, value)] for current in split_conditions]
        return split_conditions, [dict(condition) for condition in split_conditions]
    
    if additional_conditions is not None:
        _, split_conditions_list = split_fun(additional_conditions)
        dfs = []
        for conditions in split_conditions_list:
            df = sub_get_db_data(table_name, conn_fun, keywords, conditions)
            dfs.append(df)
        return pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()
    else:
        return sub_get_db_data(table_name, conn_fun, keywords)

def sub_get_db_data(table_name, conn_fun=db_oracle_connect, keywords=None, additional_conditions=None):
    conn = None
    cursor = None
    
    try:
        conn = conn_fun()
        cursor = conn.cursor()

        if keywords is None:
            keys = '*'
        else:
            keys = ','.join([f'"{key}"' for key in keywords])

        sql = f"SELECT {keys} FROM {table_name}"
        params = []
        
        if additional_conditions:
            where_clause = _build_complex_conditions(additional_conditions, params, 'ORACLE')
            if where_clause:
                sql += f' WHERE {where_clause}'

        cursor.execute(sql, params)
        results = cursor.fetchall()
        columns = [col[0] for col in cursor.description]
        df = pd.DataFrame(results, columns=columns)
        
        return df
        
    except Exception as e:
        print(e)
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def get_db_data(table_name: str, keywords: Optional[List[str]] = None, 
                additional_conditions: Optional[Dict[str, Any]] = None, 
                db_type: str = DB_TYPE) -> pd.DataFrame:
    try:
        if db_type == 'MYSQL':
            return get_mysql_db_data(table_name, keywords=keywords, additional_conditions=additional_conditions)
        elif db_type == 'DM':
            return get_dm_data(table_name, keywords=keywords, additional_conditions=additional_conditions)
        elif db_type == 'ORACLE':
            return get_oracle_db_data(table_name, keywords=keywords, additional_conditions=additional_conditions)
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
    except Exception as e:
        print(e)

