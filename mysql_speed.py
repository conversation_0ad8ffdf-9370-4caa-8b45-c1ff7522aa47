# encoding: utf-8
import time

import pandas as pd
import pymysql
import os

wind_remote = pymysql.connect(
    host='*************',
    port=3306,
    user='inforesdep01',
    password='tfyfInfo@1602',
    db='wind',
    charset='utf8'
)

db_local = pymysql.connect(
    host='localhost',
    port=3306,
    user='root',
    password='root',
    db='wind',
    charset='utf8'
)


start = time.time()
for i in range(2):
    cursor = wind_remote.cursor()
    cursor.execute("SELECT OBJECT_ID FROM cbondpricesrepo LIMIT 100000")
    results = cursor.fetchall()
print(time.time()-start)

start = time.time()
for i in range(2):
    cursor = db_local.cursor()
    cursor.execute("SELECT OBJECT_ID FROM cbondpricesrepo LIMIT 100000")
    results = cursor.fetchall()
print(time.time()-start)




start = time.time()
cursor = wind_remote.cursor()
cursor.execute("SELECT S_INFO_WINDCODE, TRADE_DT, S_DQ_TURN FROM ashareeodderivativeindicator WHERE TRADE_DT >= '20230101'")
results = cursor.fetchall()
print(time.time()-start)

start = time.time()
cursor = db_local.cursor()
cursor.execute("SELECT S_INFO_WINDCODE, TRADE_DT, S_DQ_TURN FROM ashareeodderivativeindicator WHERE TRADE_DT >= '20230101'")
results = cursor.fetchall()
print(time.time()-start)


start = time.time()
cursor = wind_remote.cursor()
cursor.execute("SELECT * FROM ashareeodprices LIMIT 1000000")
results = cursor.fetchall()
print(time.time()-start)

start = time.time()
cursor = db_local.cursor()
cursor.execute("SELECT * FROM ashareeodprices LIMIT 1000000")
results = cursor.fetchall()
print(time.time()-start)




