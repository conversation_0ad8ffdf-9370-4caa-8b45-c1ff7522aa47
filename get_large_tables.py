# encoding: utf-8
import pandas as pd
import os
import sys

# 添加路径
new_path = ['D:\\0.维护代码\\1.数据库更新维护', ]
for path in new_path:
    if path not in sys.path:
        sys.path.append(path)

from wind_database_maintain import wind_remote_fun, db_local_fun, convert_to_mysql_type

def get_all_table_names(connector):
    """获取数据库中所有表名"""
    cur = connector.cursor()
    cur.execute("SHOW TABLES")
    results = cur.fetchall()
    tables = [result[0] for result in results]
    return tables

def get_table_size(connection, table_name):
    """获取表大小(MB)"""
    cursor = connection.cursor()
    sql = f"SELECT round(((data_length + index_length) / 1024 / 1024), 2) `Size in MB` FROM information_schema.TABLES WHERE table_schema = 'wind' AND table_name = '{table_name}'"
    cursor.execute(sql)
    result = cursor.fetchone()
    return result[0] if result and result[0] else 0

def get_table_comment(connection, table_name):
    """获取表的注释"""
    cursor = connection.cursor()
    sql = f"SELECT table_comment FROM information_schema.tables WHERE table_schema = 'wind' AND table_name = '{table_name}'"
    cursor.execute(sql)
    result = cursor.fetchone()
    return result[0] if result else ""

def delete_table(connection, table_name):
    """删除指定的表"""
    try:
        cursor = connection.cursor()
        sql = f"DROP TABLE IF EXISTS {table_name}"
        cursor.execute(sql)
        connection.commit()
        return True
    except Exception as e:
        print(f"删除表 {table_name} 时出错: {str(e)}")
        return False

def find_corresponding_local_tables(remote_tables):
    """找出远程表在本地数据库中对应的表"""
    try:
        local_conn = db_local_fun()
        local_tables = get_all_table_names(local_conn)
        corresponding_tables = [table for table in remote_tables if table in local_tables]
        local_conn.close()
        return corresponding_tables
    except Exception as e:
        print(f"查找本地对应表时出错: {str(e)}")
        return []

def find_tables_with_keywords(tables, connection, keywords):
    """查找包含指定关键词的表"""
    matching_tables = []
    for table_name in tables:
        comment = get_table_comment(connection, table_name)
        size_mb = get_table_size(connection, table_name)
        
        # 检查表名或注释是否包含任何关键词
        if any(keyword.lower() in table_name.lower() or 
               (comment and keyword.lower() in comment.lower()) 
               for keyword in keywords):
            matching_tables.append({
                "表名": table_name,
                "大小(MB)": size_mb,
                "大小(GB)": round(size_mb / 1024, 2),
                "注释": comment
            })
    return matching_tables

def main():
    # 连接远程Wind数据库
    wind_remote = wind_remote_fun()
    
    try:
        # 获取所有表名
        all_tables = get_all_table_names(wind_remote)
        print(f"总共发现 {len(all_tables)} 个表")
        
        # 存储结果的列表
        large_tables = []
        discarded_tables = []
        wind_hash_tables = []
        
               
        # 遍历所有表并获取大小
        for i, table_name in enumerate(all_tables):
            print(f"正在处理第 {i+1}/{len(all_tables)} 个表: {table_name}")
            
            # 检查表名是否包含windhash
            if "windhash" in table_name.lower():
                wind_hash_tables.append(table_name)
                continue
                
            # 获取表大小(MB)
            size_mb = get_table_size(wind_remote, table_name)
            
            # 获取表注释
            comment = get_table_comment(wind_remote, table_name)
            
            # 检查是否大于20GB
            if size_mb > 20 * 1024:  # 20GB = 20 * 1024 MB
                large_tables.append({
                    "表名": table_name,
                    "大小(MB)": size_mb,
                    "大小(GB)": round(size_mb / 1024, 2),
                    "注释": comment
                })
                
            # 检查注释是否包含"废弃"
            if "废弃" in comment:
                discarded_tables.append({
                    "表名": table_name,
                    "大小(MB)": size_mb,
                    "大小(GB)": round(size_mb / 1024, 2),
                    "注释": comment
                })
        
        # 保存大于20GB的表到文件
        if large_tables:
            large_df = pd.DataFrame(large_tables)
            large_df.to_csv("large_tables.txt", sep="\t", index=False, encoding="utf-8")
            print(f"已将 {len(large_tables)} 个大于20GB的表保存到 large_tables.txt")
        else:
            print("未发现大于20GB的表")
            
        # 保存包含"废弃"的表到文件并删除其本地对应表
        if discarded_tables:
            discarded_df = pd.DataFrame(discarded_tables)
            discarded_df.to_csv("discarded_tables.txt", sep="\t", index=False, encoding="utf-8")
            print(f"已将 {len(discarded_tables)} 个包含'废弃'描述的表保存到 discarded_tables.txt")
            
            # 找出废弃表在本地数据库中对应的表
            discarded_table_names = [table["表名"] for table in discarded_tables]
            local_discarded_tables = find_corresponding_local_tables(discarded_table_names)
            
            if local_discarded_tables:
                print(f"\n发现 {len(local_discarded_tables)} 个本地废弃表，开始删除...")
                local_conn = db_local_fun()
                deleted_count = 0
                for table in local_discarded_tables:
                    if delete_table(local_conn, table):
                        deleted_count += 1
                        print(f"成功删除本地表: {table}")
                local_conn.close()
                print(f"成功删除 {deleted_count} 个本地废弃表")
            else:
                print("本地数据库中未发现对应的废弃表")
        else:
            print("未发现包含'废弃'描述的表")
            
        # 将windhash表信息写入文件
        if wind_hash_tables:
            with open("windhash_tables.txt", "w", encoding="utf-8") as f:
                for table in wind_hash_tables:
                    f.write(f"{table}\n")
            print(f"已将 {len(wind_hash_tables)} 个包含'windhash'的表保存到 windhash_tables.txt")
        else:
            print("未发现包含'windhash'的表")
        
        print("任务完成!")
        
    finally:
        wind_remote.close()

if __name__ == "__main__":
    main()