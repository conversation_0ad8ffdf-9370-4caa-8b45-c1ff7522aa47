{"Message": {"Code": 1, "Msg": ""}, "Content": {"Data": [{"DataType": 1, "Text": "中国共同基金投资组合——持股明细[ChinaMutualFundStockPortfolio]", "ID": "ChinaMutualFundStockPortfolio", "HasAuth": true, "sortOrder": -3, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": false}, {"DataType": 1, "Text": "中国共同基金投资组合——持券明细[ChinaMutualFundBondPortfolio]", "ID": "ChinaMutualFundBondPortfolio", "HasAuth": true, "sortOrder": -2, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金投资组合——其他证券[CMFOtherPortfolio]", "ID": "CMFOtherPortfolio", "HasAuth": true, "sortOrder": -1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金投资组合——行业配置[ChinaMutualFundIndPortfolio]", "ID": "ChinaMutualFundIndPortfolio", "HasAuth": true, "sortOrder": 0, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金投资组合——资产配置[ChinaMutualFundAssetPortfolio]", "ID": "ChinaMutualFundAssetPortfolio", "HasAuth": true, "sortOrder": 1, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金持有流通受限证券明细[CFundHoldRestrictedCirculation]", "ID": "CFundHoldRestrictedCirculation", "HasAuth": true, "sortOrder": 2, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金投资组合重大变动(报告期)[CFundPortfoliochanges]", "ID": "CFundPortfoliochanges", "HasAuth": true, "sortOrder": 3, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国货币式基金投资组合剩余期限(报告期)[CMMFPortfolioPTM]", "ID": "CMMFPortfolioPTM", "HasAuth": true, "sortOrder": 4, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}, {"DataType": 1, "Text": "中国共同基金投资组合第三方行业配置[CMFundThirdPartyIndPortfolio]", "ID": "CMFundThirdPartyIndPortfolio", "HasAuth": true, "sortOrder": 5, "IsContract": false, "isHotProduct": false, "isNewProduct": false, "isHasIntroduce": false, "isClientTableSame": true, "difAccountId": null, "NodeColor": "#333333", "AuthType": 4, "IsCollect": false, "IsCollectView": false, "productType": "FILESYNC", "IsMaxVersion": true}], "PageInfo": null}, "success": true}